from datetime import date, timedelta
from decimal import Decimal as D
from unittest.mock import Mock, patch

import pytest
from model_bakery import baker

from commons.dateutils import now
from core.models_grupo import <PERSON>rupo, TrechoClasse
from core.models_travel import Buseiro, Passageiro, Reserva, Travel
from core.service.reserva.rodoviaria_reserva_svc import (
    AntecedenciaInvalidaException,
    EmissaoHibrido,
    EmissaoNotAvailable,
    TicketAlreadyIssued,
)
from core.service.selecao_assento.hibrido import HibridoSeatsController
from core.service.selecao_assento.models import Assento, BlockedSeat
from integrations.rodoviaria_client.exceptions import PoltronaExpiradaException


@pytest.fixture
def mock_hibrido_seats_controller():
    """Mock do HibridoSeatsController"""
    controller = Mock(spec=HibridoSeatsController)
    return controller


@pytest.fixture
def mock_blocked_seat():
    blocked_seat = BlockedSeat(
        poltrona=Assento(numero=15, x=1, y=1, tipo_assento="convencional"),
        tempo_limite_bloqueio=(now() + timedelta(hours=24)),
        external_payload={"External": "Payload"},
    )
    return blocked_seat


@pytest.fixture
def hibrido_travel(marketplace_company):
    """Travel configurado para hibrido"""
    trecho_classe = baker.make(
        TrechoClasse,
        grupo__modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        grupo__company=marketplace_company,
        datetime_ida=now() + timedelta(hours=2),
    )

    reserva = baker.make(Reserva)

    travel = baker.make(Travel, trecho_classe=trecho_classe, reserva=reserva, max_split_value=D("50.00"))

    return travel


@pytest.fixture
def hibrido_passageiro(hibrido_travel):
    """Passageiro configurado para hibrido"""
    buseiro = baker.make(
        Buseiro,
        name="João Silva",
        cpf="*********01",
        rg_number="*********",
        phone="11999999999",
        birthday=date(1990, 1, 1),
    )

    passageiro = baker.make(Passageiro, travel=hibrido_travel, buseiro=buseiro, poltrona=15, extra={})

    return passageiro


def test_init_com_parametros_completos(hibrido_passageiro, mock_hibrido_seats_controller):
    """Testa inicialização com todos os parâmetros"""
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
    ):
        emissao = EmissaoHibrido(
            passageiro=hibrido_passageiro,
            seats_controller=mock_hibrido_seats_controller,
        )

        assert emissao.passageiro == hibrido_passageiro
        assert emissao.seats_controller == mock_hibrido_seats_controller


def test_init_ignora_antecedencia(hibrido_passageiro, mock_hibrido_seats_controller):
    """Testa inicialização com todos os parâmetros"""
    with (
        patch.object(EmissaoHibrido, "validate_antecedencia") as mock_validate_antecedencia,
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
    ):
        emissao = EmissaoHibrido(
            passageiro=hibrido_passageiro,
            seats_controller=mock_hibrido_seats_controller,
            ignore_antecedencia=True,
        )

        assert emissao.passageiro == hibrido_passageiro
        assert emissao.seats_controller == mock_hibrido_seats_controller
        mock_validate_antecedencia.assert_not_called()


def test_init_sem_seats_controller(hibrido_passageiro):
    """Testa inicialização sem seats_controller (deve criar um automaticamente)"""
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
        patch("core.service.reserva.rodoviaria_reserva_svc.HibridoSeatsController") as mock_controller_class,
    ):
        mock_controller = Mock()
        mock_controller_class.return_value = mock_controller

        emissao = EmissaoHibrido(passageiro=hibrido_passageiro)

        assert emissao.passageiro == hibrido_passageiro
        assert emissao.seats_controller == mock_controller
        mock_controller_class.assert_called_once_with(hibrido_passageiro.travel.trecho_classe)


def test_validate_antecedencia(hibrido_passageiro):
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
        patch("core.service.reserva.rodoviaria_reserva_svc.HibridoSeatsController"),
    ):
        hibrido_passageiro.travel.trecho_classe.datetime_ida = now() + timedelta(hours=4)
        with pytest.raises(
            AntecedenciaInvalidaException, match="Não é possível emitir passagem com mais de 3h de antecedência"
        ):
            EmissaoHibrido(passageiro=hibrido_passageiro)


def test_validate_modelo_venda_nao_hibrido(hibrido_passageiro):
    """Testa validação quando modelo de venda não é hibrido"""
    hibrido_passageiro.travel.trecho_classe.grupo.modelo_venda = Grupo.ModeloVenda.MARKETPLACE

    with patch("core.service.reserva.rodoviaria_reserva_svc.HibridoSeatsController"):
        with pytest.raises(EmissaoNotAvailable, match="Emissão de passageiros é permitida apenas para Hibrido"):
            EmissaoHibrido(passageiro=hibrido_passageiro)


def test_validate_viagem_nao_integrada(hibrido_passageiro):
    """Testa validação quando viagem não é integrada"""
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=False),
        patch("core.service.reserva.rodoviaria_reserva_svc.HibridoSeatsController"),
    ):
        with pytest.raises(
            EmissaoNotAvailable, match="Emissão de passageiros não é permitida para viagens não integradas"
        ):
            EmissaoHibrido(passageiro=hibrido_passageiro)


def test_validate_passagem_ja_emitida(hibrido_passageiro):
    """Testa validação quando passagem já foi emitida"""
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch(
            "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria",
            return_value=[{"id": 1}],
        ),
        patch("core.service.reserva.rodoviaria_reserva_svc.HibridoSeatsController"),
    ):
        with pytest.raises(TicketAlreadyIssued):
            EmissaoHibrido(passageiro=hibrido_passageiro)


def test_validate_sucesso(hibrido_passageiro):
    """Testa validação bem-sucedida"""
    with (
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc._is_integrado", return_value=True),
        patch("core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.get_passagens_rodoviaria", return_value=[]),
        patch("core.service.reserva.rodoviaria_reserva_svc.HibridoSeatsController"),
    ):
        # Não deve lançar exceção
        emissao = EmissaoHibrido(passageiro=hibrido_passageiro)
        assert emissao.passageiro == hibrido_passageiro


def test_emit(mocker, hibrido_passageiro, mock_hibrido_seats_controller, mock_blocked_seat):
    mocker.patch.object(EmissaoHibrido, "validate")
    mock_hibrido_seats_controller.bloquear_poltrona.return_value = mock_blocked_seat

    mock_emitir_passagem_rodoviaria = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.efetua_compra_unica", return_value="retorno"
    )
    EmissaoHibrido(hibrido_passageiro, mock_hibrido_seats_controller).emit()

    hibrido_passageiro.refresh_from_db()
    assert hibrido_passageiro.typed_extra["emissao"]["sucesso"] is True
    assert mock_emitir_passagem_rodoviaria.call_count == 1
    assert mock_emitir_passagem_rodoviaria.mock_calls[0][1][0].extra_poltronas == mock_blocked_seat.external_payload


def test_emit_erro(mocker, hibrido_passageiro, mock_hibrido_seats_controller, mock_blocked_seat):
    mocker.patch.object(EmissaoHibrido, "validate")
    mock_hibrido_seats_controller.bloquear_poltrona.return_value = mock_blocked_seat

    mock_emitir_passagem_rodoviaria = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.efetua_compra_unica", side_effect=Exception("Error")
    )
    with pytest.raises(Exception, match="Error"):
        EmissaoHibrido(hibrido_passageiro, mock_hibrido_seats_controller).emit()

    hibrido_passageiro.refresh_from_db()
    assert hibrido_passageiro.typed_extra["emissao"]["sucesso"] is False
    assert mock_emitir_passagem_rodoviaria.call_count == 1
    assert mock_emitir_passagem_rodoviaria.mock_calls[0][1][0].extra_poltronas == mock_blocked_seat.external_payload


def test_emit_erro_poltrona_expirada(mocker, hibrido_passageiro, mock_hibrido_seats_controller, mock_blocked_seat):
    mocker.patch.object(EmissaoHibrido, "validate")
    mock_blocked_seat2 = mock_blocked_seat
    mock_blocked_seat2.external_payload = {"External2": "Payload 2"}
    mock_hibrido_seats_controller.bloquear_poltrona.side_effect = [mock_blocked_seat, mock_blocked_seat2]

    mock_emitir_passagem_rodoviaria = mocker.patch(
        "core.service.reserva.rodoviaria_reserva_svc.rodoviaria_svc.efetua_compra_unica",
        side_effect=[PoltronaExpiradaException("Error"), "retorno"],
    )
    EmissaoHibrido(hibrido_passageiro, mock_hibrido_seats_controller).emit()

    hibrido_passageiro.refresh_from_db()
    assert hibrido_passageiro.typed_extra["emissao"]["sucesso"] is True
    assert mock_emitir_passagem_rodoviaria.call_count == 2
    assert mock_emitir_passagem_rodoviaria.mock_calls[0][1][0].extra_poltronas == mock_blocked_seat.external_payload
    assert mock_emitir_passagem_rodoviaria.mock_calls[1][1][0].extra_poltronas == mock_blocked_seat2.external_payload
