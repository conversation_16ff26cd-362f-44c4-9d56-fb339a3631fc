import json
from datetime import date, datetime, timedelta
from decimal import Decimal as D
from types import SimpleNamespace
from unittest import mock

import pytest
import requests
import time_machine
from django.utils import timezone
from model_bakery import baker

from buser import settings
from commons import dateutils
from commons.dateutils import now, to_default_tz_required, to_tz_required, today_midnight
from commons.django_utils import error_str
from core.enums import CategoriaEspecial
from core.forms.rodoviaria_forms import BuseiroForm, ComprarForm
from core.forms.staff_forms import (
    CadastrarGruposHibridosForm,
    CheckPaxForm,
    ClasseForm,
    CriarGruposMarketplaceForm,
    CriarRotaHibridoForm,
    ListaEmpresasAPIParams,
    ListaFormasPagamentoRodoviariaForm,
    RotaHibridoForm,
    TotalbusLoginForm,
    UpdateLinkLocalForm,
)
from core.models_commons import ActivityLog, Cidade, Profile
from core.models_company import Company
from core.models_grupo import ClosedReasons, Grupo, GrupoClasse, PriceManager, TrechoClasse
from core.models_hibrido import AutorizacaoHibrido
from core.models_rota import LocalEmbarque, LocalRetiradaMarketplace, Rota
from core.models_rotina import RotinaOnibus
from core.models_travel import Buseiro, Pagamento, Passageiro, Reserva, Travel
from core.serializers.serializer_locais_retirada import LocaisRetiradaSerializer
from core.service import globalsettings_svc, rodoviaria_svc
from core.service.reserva import rodoviaria_reserva_svc
from core.service.rodoviaria.bpe_svc import BASE_BPE_EM_CONTINGENCIA
from core.service.rodoviaria_link_trecho_classe_svc import RodoviariaTags
from core.service.rodoviaria_svc import DUPLICADO_NOVO_ESTOQUE_CLOSED_REASON
from core.service.selecao_assento import SeatSelectionConnectionError
from core.service.selecao_assento.marketplace import MarketplaceSeatsController
from core.service.selecao_assento.models import Assento, BlockedSeat, Deck, MapaPoltronasOnibus
from core.tests import fixtures
from integrations import get_client
from integrations.rodoviaria_client import RodoviariaClient
from integrations.rodoviaria_client.exceptions import (
    AutorizacaoNaoCadastradaRodoviaria,
    FetchTrechosVendidosError,
    PassengerTicketAlreadyPrintedException,
    PoltronaTrocadaException,
    RodoviariaCancelamentoException,
    RodoviariaClientError,
    RodoviariaConnectionException,
    RodoviariaException,
    RodoviariaMoverBuseiroException,
    RodoviariaNotBlockingException,
    RodoviariaOverbooking,
    RodoviariaViagemBloqueada,
    RodoviariaViagemIndisponivel,
    RotaNaoCadastradaRodoviaria,
)
from marketplace.models import TrechoClasseMarketplaceLogger
from search_result.adapter.cidade_adapter import CidadeAdapter


@pytest.fixture(autouse=True)
def remaneja_v2(globalsettings_mock):
    globalsettings_mock("rodoviaria_remanejamento_v2", True)


def test_rotinas_integradas_hibrido_list(mocker):
    mocker.patch.object(RodoviariaClient, "map_rotinas_integradas", return_value={"313": [13, 14], "100": [13, 14]})
    mocker.patch.object(
        RodoviariaClient,
        "get_empresas_integradas",
        return_value={
            "empresas": [
                {"modelo_venda": Grupo.ModeloVenda.MARKETPLACE, "company_internal_id": 313},
                {"modelo_venda": Grupo.ModeloVenda.HIBRIDO, "company_internal_id": 200},
                {"modelo_venda": Grupo.ModeloVenda.HIBRIDO, "company_internal_id": 313},
            ]
        },
    )
    rotinas_integradas = rodoviaria_svc.rotinas_integradas_hibrido_list()
    assert rotinas_integradas == [13, 14]


def test_get_map_poltronas():
    with (
        mock.patch("core.service.rodoviaria_svc._is_integrado", return_value=True),
        mock.patch.object(MarketplaceSeatsController, "get_layout_onibus") as get_layout_onibus_mock,
    ):
        get_layout_onibus_mock.return_value = MapaPoltronasOnibus(
            layout=[
                Deck(
                    andar=1,
                    assentos=[
                        Assento(livre=True, tipo_assento="Executivo", numero=1, x=0, y=0),
                        Assento(livre=False, tipo_assento="Executivo", numero=2, x=0, y=0),
                        Assento(livre=True, tipo_assento="Leito", numero=3, x=0, y=0),
                    ],
                )
            ]
        )
        trecho_classe = baker.make(
            TrechoClasse,
            grupo_classe=baker.make(GrupoClasse, tipo_assento="Executivo"),
            grupo=baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE),
        )
        assert rodoviaria_svc.get_map_poltronas(trecho_classe.id) == {"1": "livre", "2": "ocupada", "3": "ocupada"}


def test_check_poltronas_livres():
    with (
        mock.patch("core.service.rodoviaria_svc._is_integrado", return_value=True),
        mock.patch.object(
            MarketplaceSeatsController,
            "get_layout_onibus",
            return_value=MapaPoltronasOnibus(
                layout=[Deck(andar=1, assentos=[Assento(livre=True, tipo_assento="Executivo", numero=2, x=0, y=0)])]
            ),
        ),
    ):
        trecho_classe_destino = baker.make(
            TrechoClasse,
            grupo_classe=baker.make(GrupoClasse, tipo_assento="Executivo"),
            grupo=baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE),
        )
        rodoviaria_svc._check_poltronas_livres(1, 0, trecho_classe_destino)


def test_check_poltronas_livres_sem_poltronas_disponiveis():
    with (
        mock.patch("core.service.rodoviaria_svc._is_integrado", return_value=True),
        mock.patch.object(
            MarketplaceSeatsController,
            "get_layout_onibus",
            return_value=MapaPoltronasOnibus.model_validate({"layout": []}),
        ),
    ):
        trecho_classe_destino = baker.make(
            TrechoClasse,
            grupo_classe=baker.make(GrupoClasse, tipo_assento="Executivo"),
            grupo=baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE),
        )
        with pytest.raises(RodoviariaMoverBuseiroException):
            rodoviaria_svc._check_poltronas_livres(1, 0, trecho_classe_destino)


def test_check_poltronas_livres_sem_poltronas_disponiveis_para_tipo_assento():
    with (
        mock.patch("core.service.rodoviaria_svc._is_integrado", return_value=True),
        mock.patch.object(
            MarketplaceSeatsController,
            "get_layout_onibus",
            return_value=MapaPoltronasOnibus(
                layout=[Deck(andar=1, assentos=[Assento(livre=True, tipo_assento="Executivo", numero=2, x=0, y=0)])]
            ),
        ),
    ):
        trecho_classe_destino = baker.make(
            TrechoClasse,
            grupo_classe=baker.make(GrupoClasse, tipo_assento="Leito"),
            grupo=baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE),
        )
        with pytest.raises(RodoviariaMoverBuseiroException):
            rodoviaria_svc._check_poltronas_livres(1, 0, trecho_classe_destino)


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_remover_passageiro(trechoclasse_mock):
    trechoclasse_mock.closed = True
    trechoclasse_mock.closed_reason = "Motivo aleatório"
    trechoclasse_mock.save()
    travel = SimpleNamespace(
        id=5,
        trecho_classe_id=trechoclasse_mock.id,
        trecho_classe=SimpleNamespace(
            datetime_ida=timezone.now() + timedelta(hours=5), grupo=SimpleNamespace(company_id=23423423)
        ),
        status="pending",
    )
    passageiro = SimpleNamespace(buseiro_id=10, removed=True)
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as is_integrado_mock,
    ):
        is_integrado_mock.return_value = True
        efetua_cancelamento_mock.return_value = {
            "id": 1,
            "localizador": 81987,
            "status": "confirmada",
        }
        rodoviaria_svc.remove_passageiro(travel, passageiro)
        efetua_cancelamento_mock.assert_called_with(travel.id, passageiro.buseiro_id, False)
    trechoclasse_mock.refresh_from_db()
    assert trechoclasse_mock.closed is True


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_remover_passageiro_trecho_fechado_overbooking(trechoclasse_mock):
    trechoclasse_mock.closed = True
    trechoclasse_mock.closed_reason = RodoviariaOverbooking.message
    travel = SimpleNamespace(
        id=5,
        trecho_classe_id=trechoclasse_mock.id,
        trecho_classe=SimpleNamespace(
            datetime_ida=timezone.now() + timedelta(hours=5), grupo=SimpleNamespace(company_id=23423423)
        ),
        status="pending",
    )
    passageiro = SimpleNamespace(buseiro_id=10, removed=True)
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as is_integrado_mock,
    ):
        is_integrado_mock.return_value = True
        efetua_cancelamento_mock.return_value = {
            "id": 1,
            "localizador": 81987,
            "status": "confirmada",
        }
        rodoviaria_svc.remove_passageiro(travel, passageiro)
        efetua_cancelamento_mock.assert_called_with(travel.id, passageiro.buseiro_id, False)
    trechoclasse_mock.refresh_from_db()
    assert trechoclasse_mock.closed is False


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_efetua_cancelamento(trechoclasse_mock, travels_mock):
    trechoclasse_mock.closed = True
    trechoclasse_mock.closed_reason = "Motivo aleatório"
    trechoclasse_mock.save()
    travel = travels_mock[0]
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._is_prod") as _is_prod_mock,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as _is_integrado_mock,
    ):
        _is_prod_mock.return_value = True
        _is_integrado_mock.return_value = True
        efetua_cancelamento_mock.return_value = {"sucesso": "parabens"}
        assert rodoviaria_svc.efetua_cancelamento(travel) == {"sucesso": "parabens"}
        efetua_cancelamento_mock.assert_called_with(travel.id)
        trechoclasse_mock.refresh_from_db()
        assert trechoclasse_mock.closed is True  # mantém fechado


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_efetua_cancelamento_trecho_fechado_overbooking(trechoclasse_mock, travels_mock):
    trechoclasse_mock.closed = True
    trechoclasse_mock.closed_reason = RodoviariaOverbooking.message
    trechoclasse_mock.save()
    travel = travels_mock[0]
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._is_prod") as _is_prod_mock,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as _is_integrado_mock,
    ):
        _is_prod_mock.return_value = True
        _is_integrado_mock.return_value = True
        efetua_cancelamento_mock.return_value = {"sucesso": "parabens"}
        assert rodoviaria_svc.efetua_cancelamento(travel) == {"sucesso": "parabens"}
        efetua_cancelamento_mock.assert_called_with(travel.id)
        trechoclasse_mock.refresh_from_db()
        assert trechoclasse_mock.closed is False


def test_trechos_vendidos_rodoviaria():
    rota = baker.make("core.Rota")
    grupo = baker.make("core.Grupo", rota=rota)
    origem = baker.make("core.LocalEmbarque", nickname="São Paulo Tietê")
    grupo_classe = baker.make("core.GrupoClasse", tipo_assento="leito")

    destino = baker.make("core.LocalEmbarque")
    trecho_vendido = baker.make("core.TrechoVendido", origem=origem, destino=destino)
    baker.make(
        "core.TrechoClasse",
        trecho_vendido=trecho_vendido,
        grupo=grupo,
        grupo_classe=grupo_classe,
        max_split_value=D("140.00"),
        duracao_ida=timedelta(seconds=7200),
    )

    destino_2 = baker.make("core.LocalEmbarque", nickname="Taubaté")
    trecho_vendido_errado = baker.make("core.Trechovendido", origem=origem, destino=destino_2)
    tc2 = baker.make("core.TrechoClasse", trecho_vendido=trecho_vendido_errado, grupo=grupo)

    with mock.patch(
        "core.service.rodoviaria_svc._client.trechos_vendidos_rodoviaria"
    ) as trechos_vendidos_rodoviaria_mock:
        trechos_vendidos_rodoviaria_mock.return_value = [
            {
                "origem_id": origem.id,
                "destino_id": destino.id,
                "classe_buser": "leito",
                "classe_api": "leito",
                "preco": 140,
                "duracao": "02:30:00",
            }
        ]
        response = rodoviaria_svc.trechos_vendidos_rodoviaria(grupo.id)
    trechos_vendidos_rodoviaria_mock.assert_called_once_with(grupo.id)
    assert response == {
        "trechos_rodoviaria": [
            {
                "origem_id": origem.id,
                "destino_id": destino.id,
                "status": "VENDIDO_NOT_OK",
                "classe_api": "leito",
                "classe_buser": "leito",
                "preco": 140,
                "duracao": "02:30:00",
                "divergencias": "DURACAO",
            },
        ],
        "trechos_nao_existentes_api": [
            {
                "trecho_classe_id": tc2.id,
                "origem": origem.nickname,
                "destino": destino_2.nickname,
            }
        ],
    }


def test_trechos_vendidos_rodoviaria_error():
    grupo = baker.make("core.Grupo")
    with mock.patch(
        "core.service.rodoviaria_svc._client.trechos_vendidos_rodoviaria"
    ) as trechos_vendidos_rodoviaria_mock:
        trechos_vendidos_rodoviaria_mock.side_effect = FetchTrechosVendidosError(
            mensagem="Rota é muito grande para se buscar sincronamente", tipo="warning"
        )
        response = rodoviaria_svc.trechos_vendidos_rodoviaria(grupo.id)
    trechos_vendidos_rodoviaria_mock.assert_called_once_with(grupo.id)
    assert response == {"warning": "Rota é muito grande para se buscar sincronamente"}


def test_status_trecho_vendido():
    origem = SimpleNamespace(id=43)
    destino = SimpleNamespace(id=47)
    tv_rodoviaria = {
        "origem_id": origem.id,
        "destino_id": destino.id,
        "classe_api": "leito",
        "classe_buser": "leito",
        "preco": 140,
        "duracao": "02:30:00",
    }

    tc_buser = None
    tv_rodoviaria_with_status = rodoviaria_svc._trecho_vendido_with_status(tc_buser, tv_rodoviaria)
    assert tv_rodoviaria_with_status["status"] == "NAO_VENDIDO"

    tv_buser = SimpleNamespace(origem=origem, destino=destino)
    gc_buser = SimpleNamespace(tipo_assento="leito")
    tc_buser = SimpleNamespace(
        trecho_vendido=tv_buser,
        grupo_classe=gc_buser,
        max_split_value=D("170.00"),
        duracao_ida=timedelta(seconds=9600),
    )
    tv_rodoviaria_with_status = rodoviaria_svc._trecho_vendido_with_status(tc_buser, tv_rodoviaria)
    assert tv_rodoviaria_with_status["status"] == "VENDIDO_NOT_OK"
    assert tv_rodoviaria["divergencias"] == "PRECO, DURACAO"

    tc_buser.max_split_value = D("140.00")
    tc_buser.duracao_ida = timedelta(seconds=9000)
    tv_rodoviaria_with_status = rodoviaria_svc._trecho_vendido_with_status(tc_buser, tv_rodoviaria)
    assert tv_rodoviaria_with_status["status"] == "VENDIDO_OK"


def test_time_str_to_timedelta():
    timedelta_0_dias = timedelta(hours=5, minutes=30, seconds=0)
    timedelta_1_dia = timedelta(days=1, hours=5, minutes=30, seconds=0)
    timedelta_2_dias = timedelta(days=2, hours=5, minutes=30, seconds=0)
    assert rodoviaria_svc._time_str_to_timedelta(str(timedelta_0_dias)) == timedelta_0_dias
    assert rodoviaria_svc._time_str_to_timedelta(str(timedelta_1_dia)) == timedelta_1_dia
    assert rodoviaria_svc._time_str_to_timedelta(str(timedelta_2_dias)) == timedelta_2_dias


def test_is_integrado(mocker, trechoclasse_mock, empresas_integradas):
    mocker.patch.object(RodoviariaClient, "get_empresas_integradas", return_value=empresas_integradas)
    assert rodoviaria_svc._is_integrado(trechoclasse_mock.id)


def test_not_is_integrado_hibrido_com_erro_no_get_rotinas(mocker, trechoclasse_mock):
    trechoclasse_mock.grupo.modelo_venda = Grupo.ModeloVenda.HIBRIDO
    trechoclasse_mock.grupo.save()
    client: RodoviariaClient = get_client("rodoviaria")
    mocker.patch.object(client, "map_rotinas_integradas", side_effect=client.exceptions.HTTPErrorTooManyRequests)
    mocker.patch.object(client, "get_empresas_integradas", return_value={"empresas": []})
    assert not rodoviaria_svc._is_integrado(trechoclasse_mock.id)


def test_is_integrado_hibrido_pela_rotina(globalsettings_mock, mocker, trechoclasse_mock):
    trechoclasse_mock.grupo.modelo_venda = Grupo.ModeloVenda.HIBRIDO
    trechoclasse_mock.grupo.rotina_onibus = baker.make(RotinaOnibus)
    trechoclasse_mock.grupo.company = None
    trechoclasse_mock.grupo.save()
    globalsettings_mock("rotinas_hibrido_integradas_rode_rotas", [trechoclasse_mock.grupo.rotina_onibus_id])
    client: RodoviariaClient = get_client("rodoviaria")
    mocker.patch.object(
        client,
        "get_empresas_integradas",
        return_value={"empresas": [{"company_internal_id": 313, "modelo_venda": Grupo.ModeloVenda.HIBRIDO}]},
    )
    assert rodoviaria_svc._is_integrado(trechoclasse_mock.id)


def test_empresas_by_features(empresas_integradas):
    with mock.patch("core.service.rodoviaria_svc._client.get_empresas_by_features") as get_empresas_by_features_mock:
        get_empresas_by_features_mock.return_value = empresas_integradas
        rodoviaria_svc.empresas_by_features(features=["itinerario"])
        get_empresas_by_features_mock.assert_called_once_with(["itinerario"])


def test_empresas_by_features_not_prod(globalsettings_mock):
    globalsettings_mock("rodoviaria_prod", False)
    resp = rodoviaria_svc.empresas_by_features(features=["itinerario"])
    assert resp == []


def test_is_integrado_not_marketplace(trechoclasse_mock):
    trechoclasse_mock.grupo.modelo_venda = "buser"
    trechoclasse_mock.grupo.save()
    assert not rodoviaria_svc._is_integrado(trechoclasse_mock.id)


def test_add_multiple_pax_async():
    pax_form = CheckPaxForm.parse_obj(
        {
            "trechoclasseId": 3123,
            "travelId": 5423,
            "valorPorBuseiro": 65,
            "passenger": {
                "buseiroId": 8392,
                "name": "Passageiro 1",
                "cpf": "56631603021",
                "rgNumber": "*********",
                "phone": "12988776655",
                "buyer_cpf": "12988776655",
            },
            "idOrigem": 10,
            "idDestino": 20,
        }
    )
    with mock.patch("core.service.rodoviaria_svc._client.add_multiple_pax_async") as mock_add_multiple_pax_async:
        rodoviaria_svc.add_multiple_pax_async([pax_form], modelo_venda=Grupo.ModeloVenda.HIBRIDO)
    mock_add_multiple_pax_async.assert_called_once_with([pax_form.dict()], Grupo.ModeloVenda.HIBRIDO)


def test_not_integrado(trechoclasse_mock, marketplace_company):
    with mock.patch("core.service.rodoviaria_svc._client.get_empresas_integradas") as get_empresas_integradas_mock:
        get_empresas_integradas_mock.return_value = {"empresas": []}
        assert not rodoviaria_svc._is_integrado(trechoclasse_mock.id)


@mock.patch("core.service.rodoviaria_svc._client.get_passageiros", return_value=[])
def test_raise_for_antecedencia_cancelamento_sem_passagens_para_cancelar(mock_get_passageiros, travels_mock):
    travel = travels_mock[0]
    result = rodoviaria_svc._raise_for_antecedencia_cancelamento(travel)
    mock_get_passageiros.assert_called_once_with([travel.id])
    assert result is None


@mock.patch("core.service.rodoviaria_svc._client.get_passageiros", return_value=[])
def test_raise_for_antecedencia_cancelamento_horario_valido(mock_get_passageiros, travels_mock):
    travel = travels_mock[0]
    travel.trecho_classe.datetime_ida = timezone.now() + timedelta(hours=4)
    travel.trecho_classe.save()
    result = rodoviaria_svc._raise_for_antecedencia_cancelamento(travel)
    mock_get_passageiros.assert_not_called()
    assert result is None


@mock.patch("core.service.rodoviaria_svc._client.get_passageiros", return_value=["a"])
def test_raise_for_antecedencia_cancelamento_horario_invalido(mock_get_passageiros, travels_mock):
    travel = travels_mock[0]
    travel.trecho_classe.datetime_ida = timezone.now() + timedelta(hours=1)
    travel.trecho_classe.save()
    with pytest.raises(RodoviariaCancelamentoException):
        rodoviaria_svc._raise_for_antecedencia_cancelamento(travel)
    mock_get_passageiros.assert_called_once_with([travel.id])


@mock.patch("core.service.rodoviaria_svc._client.get_passageiros", return_value=["a"])
def test_raise_for_antecedencia_cancelamento_horario_invalido_empresa_custom(mock_get_passageiros, travels_mock):
    travel = travels_mock[0]
    travel.trecho_classe.datetime_ida = timezone.now() + timedelta(hours=10)
    travel.trecho_classe.grupo.company_id = 6227
    travel.trecho_classe.save()
    with pytest.raises(RodoviariaCancelamentoException):
        rodoviaria_svc._raise_for_antecedencia_cancelamento(travel)
    mock_get_passageiros.assert_called_once_with([travel.id])


@mock.patch("core.service.rodoviaria_svc._client.get_passageiros", return_value=[])
def test_raise_for_antecedencia_cancelamento_horario_valido_empresa_custom(mock_get_passageiros, travels_mock):
    travel = travels_mock[0]
    travel.trecho_classe.datetime_ida = timezone.now() + timedelta(hours=15)
    travel.trecho_classe.grupo.company_id = 6227
    travel.trecho_classe.save()
    result = rodoviaria_svc._raise_for_antecedencia_cancelamento(travel)
    mock_get_passageiros.assert_not_called()
    assert result is None


def test_remover_pax_da_lista(trechoclasse_mock, travels_mock, passageiro_mock):
    trechoclasse_mock.closed = True
    trechoclasse_mock.closed_reason = "Motivo aleatório"
    trechoclasse_mock.save()
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento"),
        mock.patch("core.service.rodoviaria_svc.lista_passageiros_viagem") as lista_passageiros_viagem_mock,
        mock.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento"),
    ):
        lista_passageiros_viagem_mock.return_value = [
            {
                "company_id": trechoclasse_mock.grupo.company_id,
                "grupo_id": trechoclasse_mock.grupo_id,
            }
        ]
        assert (
            rodoviaria_svc.remover_pax_da_lista(
                {
                    "travel_id": travels_mock[0].id,
                    "buseiro_id": passageiro_mock.buseiro_id,
                    "grupo": trechoclasse_mock.grupo,
                }
            )
            == lista_passageiros_viagem_mock.return_value
        )
        trechoclasse_mock.refresh_from_db()
        assert trechoclasse_mock.closed is True


def test_remover_pax_da_lista_trecho_fechado_overbooking(trechoclasse_mock, travels_mock, passageiro_mock):
    trechoclasse_mock.closed = True
    trechoclasse_mock.closed_reason = RodoviariaOverbooking.message
    trechoclasse_mock.save()
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento"),
        mock.patch("core.service.rodoviaria_svc.lista_passageiros_viagem") as lista_passageiros_viagem_mock,
        mock.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento"),
    ):
        lista_passageiros_viagem_mock.return_value = [
            {
                "company_id": trechoclasse_mock.grupo.company_id,
                "grupo_id": trechoclasse_mock.grupo_id,
            }
        ]
        assert (
            rodoviaria_svc.remover_pax_da_lista(
                {
                    "travel_id": travels_mock[0].id,
                    "buseiro_id": passageiro_mock.buseiro_id,
                    "grupo": trechoclasse_mock.grupo,
                }
            )
            == lista_passageiros_viagem_mock.return_value
        )
        trechoclasse_mock.refresh_from_db()
        assert trechoclasse_mock.closed is False


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 15, 34, 30)))
def test_remover_pax_da_lista_dentro_tres_horas_hibrido(trechoclasse_mock, travels_mock, passageiro_mock):
    trechoclasse_mock.grupo.modelo_venda = Grupo.ModeloVenda.HIBRIDO
    trechoclasse_mock.datetime_ida = timezone.now() + timedelta(hours=2)
    trechoclasse_mock.grupo.save()
    trechoclasse_mock.save()
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc.lista_passageiros_viagem") as lista_passageiros_viagem_mock,
    ):
        lista_passageiros_viagem_mock.return_value = [
            {
                "company_id": trechoclasse_mock.grupo.company_id,
                "grupo_id": trechoclasse_mock.grupo_id,
            }
        ]
        assert (
            rodoviaria_svc.remover_pax_da_lista(
                {
                    "travel_id": travels_mock[0].id,
                    "buseiro_id": passageiro_mock.buseiro_id,
                    "grupo": trechoclasse_mock.grupo,
                }
            )
            == lista_passageiros_viagem_mock.return_value
        )
        efetua_cancelamento_mock.assert_called_once()
        lista_passageiros_viagem_mock.assert_called_once()


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_remover_pax_da_lista_dentro_tres_horas_marketplace(trechoclasse_mock, travels_mock, passageiro_mock):
    trechoclasse_mock.grupo.modelo_venda = Grupo.ModeloVenda.MARKETPLACE
    trechoclasse_mock.datetime_ida = timezone.now() + timedelta(hours=2)
    trechoclasse_mock.grupo.save()
    trechoclasse_mock.save()
    with (
        mock.patch("core.service.rodoviaria_svc._client.get_passageiros"),
        pytest.raises(RodoviariaCancelamentoException),
    ):
        rodoviaria_svc.remover_pax_da_lista(
            {
                "travel_id": travels_mock[0].id,
                "buseiro_id": passageiro_mock.buseiro_id,
                "grupo": trechoclasse_mock.grupo,
            }
        )


def test_add_pax_na_lista_lista_passageiros(trechoclasse_mock, passageiro_mock):
    with (
        mock.patch("core.service.rodoviaria_svc._client.add_pax_na_lista"),
        mock.patch("core.service.rodoviaria_svc.lista_passageiros_viagem") as lista_passageiros_viagem_mock,
    ):
        lista_passageiros_viagem_mock.return_value = [
            {
                "id": 1,
                "poltrona": 10,
                "localizador": "1234",
                "buseiro_id": passageiro_mock.buseiro_id,
            }
        ]
        lista_passageiros = rodoviaria_svc.add_pax_na_lista({"trechoclasse_id": trechoclasse_mock.id})
        assert lista_passageiros == lista_passageiros_viagem_mock.return_value


def test_add_pax_na_lista_nao_lista_passageiros(trechoclasse_mock, passageiro_mock):
    with (
        mock.patch("core.service.rodoviaria_svc._client.add_pax_na_lista") as mock_add_pax,
        mock.patch("core.service.rodoviaria_svc.lista_passageiros_viagem") as lista_passageiros_viagem_mock,
    ):
        response = rodoviaria_svc.add_pax_na_lista({"trechoclasse_id": trechoclasse_mock.id}, listar_passageiros=False)
    assert response == mock_add_pax.return_value
    lista_passageiros_viagem_mock.assert_not_called()


def test_lista_passageiros_viagem(passageiro_mock, travels_mock):
    with mock.patch("core.service.rodoviaria_svc._client.get_passageiros") as get_passageiros_mock:
        get_passageiros_mock.return_value = [
            {
                "id": 99,
                "poltrona": 10,
                "localizador": "*********",
                "buseiro_id": passageiro_mock.buseiro_id,
                "numero_passagem": "12233",
                "status": "confirmada",
                "travel_id": 19,
            }
        ]
        passageiros = rodoviaria_svc.lista_passageiros_viagem(travels_mock[0].grupo_id)
        assert len(passageiros) == 1
        passageiro = passageiros[0]
        origem = passageiro_mock.travel.trecho_classe.trecho_vendido.origem.cidade
        destino = passageiro_mock.travel.trecho_classe.trecho_vendido.destino.cidade
        assert passageiro["nome"] == passageiro_mock.buseiro.name
        assert passageiro["origem"] == origem.name
        assert passageiro["destino"] == destino.name
        assert passageiro["id_origem"] == origem.id
        assert passageiro["id_destino"] == destino.id
        assert passageiro["cpf"] is None
        assert passageiro["documento"] == "*********"
        assert passageiro["poltrona"] == 10
        assert passageiro["localizador"] == "*********"
        assert passageiro["passagem"] == "12233"
        assert passageiro["status"] == "confirmada"
        assert passageiro["travel_id"] == 19


def test_lista_passageiros_viagem_passagem_com_origem_e_destino(passageiro_mock, travels_mock):
    with mock.patch("core.service.rodoviaria_svc._client.get_passageiros") as get_passageiros_mock:
        get_passageiros_mock.return_value = [
            {
                "id": 99,
                "poltrona": 10,
                "localizador": "*********",
                "buseiro_id": passageiro_mock.buseiro_id,
                "numero_passagem": "12233",
                "status": "confirmada",
                "travel_id": 19,
                "origem": "ORIGEM",
                "destino": "DESTINO",
            }
        ]
        passageiros = rodoviaria_svc.lista_passageiros_viagem(travels_mock[0].grupo_id)
        assert len(passageiros) == 1
        passageiro = passageiros[0]
        origem = passageiro_mock.travel.trecho_classe.trecho_vendido.origem.cidade
        destino = passageiro_mock.travel.trecho_classe.trecho_vendido.destino.cidade
        assert passageiro["nome"] == passageiro_mock.buseiro.name
        assert passageiro["origem"] == "ORIGEM"
        assert passageiro["destino"] == "DESTINO"
        assert passageiro["id_origem"] == origem.id
        assert passageiro["id_destino"] == destino.id
        assert passageiro["cpf"] is None
        assert passageiro["documento"] == "*********"
        assert passageiro["poltrona"] == 10
        assert passageiro["localizador"] == "*********"
        assert passageiro["passagem"] == "12233"
        assert passageiro["status"] == "confirmada"
        assert passageiro["travel_id"] == 19


def test_update_link_local_embarque(locais_mock):
    local = locais_mock.l1
    data = {
        "link_id": 1,
        "local_embarque_buser_id": local.id,
        "cidade_embarque_buser_id": local.cidade.id,
        "editar_link": True,
    }
    params = UpdateLinkLocalForm.parse_raw(json.dumps(data))
    with mock.patch(
        "core.service.rodoviaria_svc._client.update_link_local_embarque"
    ) as mock_client_update_link_local_embarque:
        mock_client_update_link_local_embarque.return_value = True
        response = rodoviaria_svc.update_link_local_embarque(params)
    assert response is True


def test_update_link_local_embarque_create_local_retirada(locais_mock, marketplace_company):
    local = locais_mock.l1
    data = {
        "link_id": 1,
        "local_embarque_buser_id": local.id,
        "cidade_embarque_buser_id": local.cidade.id,
        "editar_link": False,
        "local_retirada": {
            "id": None,
            "local_embarque_id": local.id,
            "company_id": marketplace_company.id,
            "tipo": "guiche",
            "descricao": "Primar Turismo",
        },
    }
    params = UpdateLinkLocalForm.parse_raw(json.dumps(data))
    with mock.patch(
        "core.service.rodoviaria_svc._client.update_link_local_embarque"
    ) as mock_client_update_link_local_embarque:
        mock_client_update_link_local_embarque.return_value = True
        response = rodoviaria_svc.update_link_local_embarque(params)
    assert response == {"sucesso": "Local de retirada cadastrado"}
    assert LocalRetiradaMarketplace.objects.filter(
        company_id=marketplace_company.id, local_embarque_id=local.id
    ).exists()


def test_update_link_local_embarque_edit_local_retirada(locais_mock, marketplace_company):
    local = locais_mock.l1
    local_retirada = baker.make(
        LocalRetiradaMarketplace,
        company=marketplace_company,
        local_embarque=local,
        descricao="Primar Turismo",
        tipo="guiche",
    )
    data = {
        "link_id": 1,
        "local_embarque_buser_id": local.id,
        "cidade_embarque_buser_id": local.cidade.id,
        "editar_link": False,
        "local_retirada": {
            "id": local_retirada.id,
            "local_embarque_id": local.id,
            "company_id": marketplace_company.id,
            "tipo": "motorista",
            "descricao": "Plataforma 29",
        },
    }
    params = UpdateLinkLocalForm.parse_raw(json.dumps(data))
    with mock.patch(
        "core.service.rodoviaria_svc._client.update_link_local_embarque"
    ) as mock_client_update_link_local_embarque:
        mock_client_update_link_local_embarque.return_value = True
        response = rodoviaria_svc.update_link_local_embarque(params)
    assert response == {"sucesso": "Local de retirada cadastrado"}
    local_retirada.refresh_from_db()
    assert local_retirada.descricao == "Plataforma 29"
    assert local_retirada.tipo == "motorista"


def test_update_link_local_embarque_error_local():
    data = {
        "link_id": 1,
        "local_embarque_buser_id": 7218,
        "cidade_embarque_buser_id": 26,
        "editar_link": True,
    }
    assert LocalEmbarque.objects.filter(id=data["local_embarque_buser_id"]).exists() is False
    params = UpdateLinkLocalForm.parse_raw(json.dumps(data))
    resp = rodoviaria_svc.update_link_local_embarque(params)
    assert resp["error"] == "Erro ao encontrar local de embarque do buser_django"


def test_update_link_local_embarque_error_cidade(locais_mock):
    data = {
        "link_id": 1,
        "local_embarque_buser_id": locais_mock.l1.id,
        "cidade_embarque_buser_id": 2618,
        "editar_link": True,
    }
    assert LocalEmbarque.objects.filter(id=data["cidade_embarque_buser_id"]).exists() is False
    params = UpdateLinkLocalForm.parse_raw(json.dumps(data))
    resp = rodoviaria_svc.update_link_local_embarque(params)
    assert resp["error"] == "Erro ao encontrar cidade de embarque do buser_django"


def test_list_links_locais_embarque(locais_mock, marketplace_company):
    local_retirada = baker.make(
        LocalRetiradaMarketplace,
        tipo="motorista",
        company=marketplace_company,
        local_embarque=locais_mock.l1,
    )
    with mock.patch("core.service.rodoviaria_svc._client.list_links_local_embarque") as list_links_local_embarque_mock:
        list_links_local_embarque_mock.return_value = {
            "items": [
                {
                    "id": 1,
                    "empresa_id": marketplace_company.id,
                    "empresa_rodoviaria_id": 2931,
                    "cidade_external": "Goiânia #5208707",
                    "localidade_external": "Terminal Rodoviário de Goiânia #2",
                    "local_embarque_buser": locais_mock.l1.id,
                    "buser_cidade_id": locais_mock.c1.id,
                },
                {
                    "id": 2,
                    "empresa_id": marketplace_company.id,
                    "empresa_rodoviaria_id": 2931,
                    "cidade_external": "São Paulo #5208707",
                    "localidade_external": "Terminal Rodoviário do Tietê - São Paulo #2",
                    "local_embarque_buser": locais_mock.l2.id,
                    "buser_cidade_id": locais_mock.c2.id,
                },
            ],
            "count": 2,
            "num_pages": 1,
        }
        response = rodoviaria_svc.list_links_locais_embarque(
            {
                "empresa_id_filter": 2931,
                "paginator": {"sortBy": "local_retirada", "descending": False},
            }
        )
        item_0 = response.get("items")[0]  # ordenado pelo local de retirada (o que não tem local_retirada vem antes)
        assert item_0.get("local_embarque_buser").get("nickname") == "local2"
        item_1 = response.get("items")[1]
        assert item_1.get("empresa").get("name") == "Primar"
        assert item_1.get("local_embarque_buser").get("nickname") == "local1"
        assert item_1["local_retirada"] == LocaisRetiradaSerializer().serialize_object(local_retirada)


def test_cancelar_bpes(travels_mock):
    with mock.patch(
        "core.service.rodoviaria_svc._client.solicitar_cancelamento_travels",
        return_value={},
    ) as solicita_cancelamento_travels_mock:
        rodoviaria_svc.cancelar_bpes([travels_mock[0].grupo_id])
    solicita_cancelamento_travels_mock.assert_called_once_with(sorted([travel.id for travel in travels_mock]))


def _bpe_mocked_response(passagem_id, buseiro_id, travel_id, bpe_em_contingencia=False):
    return {
        "linha": "GOIÂNIA (GO) X INHUMAS (GO)",
        "prefixo": "16015831",
        "servico": "1020",
        "travel_id": travel_id,
        "passagens": [
            {
                "bpe": {
                    "chave": "41210891873372000692630010000037231014912221",
                    "data_autorizacao": "11/08/2021 17:08:52",
                    "numero": "3723",
                    "protocolo_autorizacao": "141210006048505",
                    "serie": "001",
                    "tipo": "Normal",
                },
                "bpe_qrcode": "https://bpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31200641550112006304630550000132511509753679&tpAmb=2",
                "desconto": 60.7,
                "localizador": "010000079011",
                "monitriip": "312006415501120063046305500001325115097536790000132510MARINHO202006180600000100000119170000000000000000002565000072",
                "numero_bilhete": "10000000053566",
                "outras_taxas": 0.0,
                "pedagio": 0.0,
                "poltrona": 10,
                "seguro": 0.0,
                "tarifa": 224.82,
                "taxa_de_embarque": 5.49,
                "troco": "0.00",
                "valor_pago": 169.61,
                "valor_pgto": 169.61,
                "valor_total": 230.31,
                "buseiro_id": buseiro_id,
                "prefixo": "16015831",
                "servico": "1020",
                "linha": "GOIÂNIA (GO) X INHUMAS (GO)",
                "bpe_em_contingencia": bpe_em_contingencia,
                "passagem_id": passagem_id,
            }
        ],
        "outros_tributos": "ICMS:12,30 (10,00%) OUTROS TRIB:13,53 (11,00%)",
    }


def test_dados_bpe_passagem(mocker, travels_mock, requests_mock, passageiro_mock):
    passagem_id = 5831003
    mocked_response = _bpe_mocked_response(passagem_id, passageiro_mock.buseiro_id, passageiro_mock.travel_id)
    client: RodoviariaClient = get_client("rodoviaria")
    mocker.patch.object(client, "dados_bpe_passagem", return_value=mocked_response)

    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/empresas",
        json={"empresas": []},
        status_code=200,
    )
    resp = rodoviaria_svc.dados_bpe_passagem(travels_mock[0])
    assert resp["travel_id"] == travels_mock[0].id
    assert resp["linha"] == "GOIÂNIA (GO) X INHUMAS (GO)"
    assert resp["prefixo"] == "16015831"
    assert resp["servico"] == "1020"
    assert resp["endereco"] == "ABC, 90, DEF, São Paulo, SP"
    assert resp["origem"] == "cidade1 - SP"
    assert resp["destino"] == "cidade2 - MG"
    assert resp["data"]
    assert resp["hora"]
    passagens = resp["passagens"]
    assert isinstance(passagens, list)
    passagem = passagens[0]
    assert passagem["bpe"]["chave"] == "41210891873372000692630010000037231014912221"
    assert passagem["bpe"]["data_autorizacao"] == "11/08/2021 17:08:52"
    assert passagem["bpe"]["numero"] == "3723"
    assert passagem["bpe"]["protocolo_autorizacao"] == "141210006048505"
    assert passagem["bpe"]["serie"] == "001"
    assert passagem["bpe"]["tipo"] == "Normal"
    assert passagem["passageiro"]["nome"] == "Fulano de Tal"
    assert passagem["passageiro"]["documento"] == "*********"
    assert (
        passagem["bpe_qrcode"]
        == "https://bpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31200641550112006304630550000132511509753679&tpAmb=2"
    )
    assert passagem["desconto"] == 60.7
    assert passagem["localizador"] == "010000079011"
    assert (
        passagem["monitriip"]
        == "312006415501120063046305500001325115097536790000132510MARINHO202006180600000100000119170000000000000000002565000072"
    )
    assert passagem["numero_bilhete"] == "10000000053566"
    assert passagem["outras_taxas"] == 0.0
    assert passagem["pedagio"] == 0.0
    assert passagem["poltrona"] == 10
    assert passagem["seguro"] == 0.0
    assert passagem["tarifa"] == 224.82
    assert passagem["taxa_de_embarque"] == 5.49
    assert passagem["troco"] == "0.00"
    assert passagem["valor_pago"] == 169.61
    assert passagem["valor_pgto"] == 169.61
    assert passagem["valor_total"] == 230.31
    assert passagem["endereco"] == "ABC, 90, DEF, São Paulo, SP"
    assert passagem["cnpj"] == ""
    assert passagem["buseiro_id"] == passageiro_mock.buseiro_id
    assert passagem["linha"] == "GOIÂNIA (GO) X INHUMAS (GO)"
    assert passagem["prefixo"] == "16015831"
    assert passagem["servico"] == "1020"
    assert passagem["origem"] == "cidade1 - SP"
    assert passagem["destino"] == "cidade2 - MG"
    assert passagem["data"]
    assert passagem["hora"]
    assert passagem["bpe_em_contingencia"] is False
    assert passagem["passagem_id"] == passagem_id


def test_dados_bpe_passagem_forbidden(mocker, travels_mock, requests_mock, passageiro_mock):
    client: RodoviariaClient = get_client("rodoviaria")
    mocker.patch.object(client, "dados_bpe_passagem", side_effect=client.exceptions.HTTPErrorForbidden)

    resp = rodoviaria_svc.dados_bpe_passagem(travels_mock[0])
    assert resp == {}


def test_dados_bpe_passagem_num_queries(
    mocker, travels_mock, requests_mock, passageiro_mock, django_assert_num_queries
):
    passagem_id = 5831003
    mocked_response = _bpe_mocked_response(passagem_id, passageiro_mock.buseiro_id, passageiro_mock.travel_id)
    client: RodoviariaClient = get_client("rodoviaria")
    mocker.patch.object(client, "dados_bpe_passagem", return_value=mocked_response)

    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/empresas",
        json={"empresas": []},
        status_code=200,
    )
    with django_assert_num_queries(3):
        rodoviaria_svc.dados_bpe_passagem(travels_mock[0])


def test_dados_bpe_passagem_em_contingencia(mocker, travels_mock, requests_mock, passageiro_mock):
    passagem_id = 53481
    mocked_response = _bpe_mocked_response(
        passagem_id, passageiro_mock.buseiro_id, passageiro_mock.travel_id, bpe_em_contingencia=True
    )
    client: RodoviariaClient = get_client("rodoviaria")
    mocker.patch.object(client, "dados_bpe_passagem", return_value=mocked_response)

    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/empresas",
        json={"empresas": []},
        status_code=200,
    )
    resp = rodoviaria_svc.dados_bpe_passagem(travels_mock[0])
    passagem = resp["passagens"][0]
    assert passagem["bpe_qrcode"] == BASE_BPE_EM_CONTINGENCIA.format(passagem_id=passagem_id)
    assert passagem["bpe_em_contingencia"] is True
    assert passagem["passagem_id"] == passagem_id


def test_dados_bpe_passagem_batch(travels_mock, requests_mock, passageiro_mock):
    passagem_id = 98312
    mocked_response = [_bpe_mocked_response(passagem_id, passageiro_mock.buseiro_id, passageiro_mock.travel_id)]
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/dados_bpe_passagem_batch?travel_ids={travels_mock[0].id}",
        json=mocked_response,
        status_code=200,
    )

    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/empresas",
        json={"empresas": []},
        status_code=200,
    )
    items = rodoviaria_svc.dados_bpe_passagem_batch(travels_mock[0].grupo)
    resp = items[0]
    assert resp["travel_id"] == travels_mock[0].id
    assert resp["linha"] == "GOIÂNIA (GO) X INHUMAS (GO)"
    assert resp["prefixo"] == "16015831"
    assert resp["servico"] == "1020"
    assert resp["endereco"] == "ABC, 90, DEF, São Paulo, SP"
    assert resp["origem"] == "cidade1 - SP"
    assert resp["destino"] == "cidade2 - MG"
    assert resp["data"]
    assert resp["hora"]
    passagens = resp["passagens"]
    assert isinstance(passagens, list)
    passagem = passagens[0]
    assert passagem["bpe"]["chave"] == "41210891873372000692630010000037231014912221"
    assert passagem["bpe"]["data_autorizacao"] == "11/08/2021 17:08:52"
    assert passagem["bpe"]["numero"] == "3723"
    assert passagem["bpe"]["protocolo_autorizacao"] == "141210006048505"
    assert passagem["bpe"]["serie"] == "001"
    assert passagem["bpe"]["tipo"] == "Normal"
    assert passagem["passageiro"]["nome"] == "Fulano de Tal"
    assert passagem["passageiro"]["documento"] == "*********"
    assert (
        passagem["bpe_qrcode"]
        == "https://bpe.fazenda.mg.gov.br/portalbpe/sistema/qrcode.xhtml?chBPe=31200641550112006304630550000132511509753679&tpAmb=2"
    )
    assert passagem["desconto"] == 60.7
    assert passagem["localizador"] == "010000079011"
    assert (
        passagem["monitriip"]
        == "312006415501120063046305500001325115097536790000132510MARINHO202006180600000100000119170000000000000000002565000072"
    )
    assert passagem["numero_bilhete"] == "10000000053566"
    assert passagem["outras_taxas"] == 0.0
    assert passagem["pedagio"] == 0.0
    assert passagem["poltrona"] == 10
    assert passagem["seguro"] == 0.0
    assert passagem["tarifa"] == 224.82
    assert passagem["taxa_de_embarque"] == 5.49
    assert passagem["troco"] == "0.00"
    assert passagem["valor_pago"] == 169.61
    assert passagem["valor_pgto"] == 169.61
    assert passagem["valor_total"] == 230.31
    assert passagem["endereco"] == "ABC, 90, DEF, São Paulo, SP"
    assert passagem["cnpj"] == ""
    assert passagem["buseiro_id"] == passageiro_mock.buseiro_id
    assert passagem["linha"] == "GOIÂNIA (GO) X INHUMAS (GO)"
    assert passagem["prefixo"] == "16015831"
    assert passagem["servico"] == "1020"
    assert passagem["origem"] == "cidade1 - SP"
    assert passagem["destino"] == "cidade2 - MG"
    assert passagem["data"]
    assert passagem["hora"]


def test_dados_bpe_passagem_batch_num_queries(travels_mock, requests_mock, django_assert_num_queries):
    buseiro_1 = baker.make("Buseiro", name="Fulano de Tal", rg_number="*********")
    passageiro_1 = baker.make("Passageiro", travel=travels_mock[0], buseiro=buseiro_1)
    buseiro_2 = baker.make("Buseiro", name="Fulano de Tal 2", rg_number="*********")
    passageiro_2 = baker.make("Passageiro", travel=travels_mock[1], buseiro=buseiro_2)
    passagem_id_1 = 98312
    passagem_id_2 = 98312
    mocked_response = [
        _bpe_mocked_response(passagem_id_1, passageiro_1.buseiro_id, passageiro_1.travel_id),
        _bpe_mocked_response(passagem_id_2, passageiro_2.buseiro_id, passageiro_2.travel_id),
    ]
    mock_get_batch_bpe = requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/dados_bpe_passagem_batch?travel_ids={travels_mock[0].id}",
        json=mocked_response,
        status_code=200,
    )

    mock_get_empresas = requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/empresas",
        json={"empresas": []},
        status_code=200,
    )
    grupo = travels_mock[0].grupo
    with django_assert_num_queries(4):
        rodoviaria_svc.dados_bpe_passagem_batch(grupo)
    assert mock_get_empresas.called_once
    assert mock_get_batch_bpe.called_once


def test_dados_bpe_passagem_company_sem_endereco(mocker, travels_mock, requests_mock, passageiro_mock):
    company = travels_mock[0].trecho_classe.grupo.company
    company.endereco_logradouro = None
    company.endereco_bairro = None
    company.razao_social = "Primar"
    company.save()
    passagem_id = 13231
    mocked_response = _bpe_mocked_response(passagem_id, passageiro_mock.buseiro_id, passageiro_mock.travel_id)
    client: RodoviariaClient = get_client("rodoviaria")
    mocker.patch.object(client, "dados_bpe_passagem", return_value=mocked_response)
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/empresas",
        json={"empresas": []},
        status_code=200,
    )

    resp = rodoviaria_svc.dados_bpe_passagem(travels_mock[0])
    assert resp["travel_id"] == travels_mock[0].id
    assert resp["endereco"] == "São Paulo, SP"
    company.endereco_logradouro = "ABC, 90"
    company.endereco_bairro = None
    company.save()
    resp = rodoviaria_svc.dados_bpe_passagem(travels_mock[0])
    assert resp["travel_id"] == travels_mock[0].id
    assert resp["endereco"] == "ABC, 90, São Paulo, SP"
    company.endereco_logradouro = None
    company.endereco_bairro = "DEF"
    company.save()
    resp = rodoviaria_svc.dados_bpe_passagem(travels_mock[0])
    assert resp["travel_id"] == travels_mock[0].id
    assert resp["endereco"] == "DEF, São Paulo, SP"


def test_view_dados_bpe_passagem_error_unauthorized(client, user, travels_mock):
    user.id += 1
    user.username = "zezinho"
    user.save()
    client.force_login(user)
    resp = client.post(f"/api/reserva/bpe/{travels_mock[0].reservation_code}")
    assert resp.status_code == 401


def test_view_dados_bpe_passagem_error_not_found_travel(client, user, travels_mock):
    user.id += 1
    user.username = "zezinho"
    user.save()
    client.force_login(user)
    resp = client.post(f"/api/reserva/bpe/{travels_mock[0].reservation_code[::-1]}")
    assert resp.status_code == 404


def test_view_dados_bpe_passagem_error_pending_payment(client, user, travels_mock):
    user.id += 1
    user.username = "zezinho"
    user.save()
    client.force_login(user)
    travels_mock[0].pagamento = baker.make(Pagamento, status=Pagamento.Status.PENDING_CHALLENGE)
    travels_mock[0].user = user
    travels_mock[0].save()
    resp = client.post(f"/api/reserva/bpe/{travels_mock[0].reservation_code}")
    assert resp.status_code == 404


def test_view_dados_bpe_passagem_error_not_found_bpe(mocker, client, user, travels_mock):
    user.id += 1
    user.username = "zezinho"
    user.save()
    client.force_login(user)
    travels_mock[0].user = user
    travels_mock[0].save()
    mocker.patch("core.service.rodoviaria_svc.dados_bpe_passagem", return_value={})
    resp = client.post(f"/api/reserva/bpe/{travels_mock[0].reservation_code}")
    assert resp.status_code == 404


def test_has_bpe(requests_mock, travels_mock, mock_is_integrado):
    mocked_response = {"has_bpe": True}
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/has_bpe",
        json=mocked_response,
        status_code=200,
    )
    resp = rodoviaria_svc.has_bpe(travels_mock[0])
    assert resp is not None
    assert resp["has_bpe"] is True


def test_has_bpe_com_pagamento_not_ok(requests_mock, travels_mock, mock_is_integrado):
    travels_mock[0].pagamento = baker.make(Pagamento, status=Pagamento.Status.PENDING_CHALLENGE)
    resp = rodoviaria_svc.has_bpe(travels_mock[0])
    assert resp is None


def test_has_bpe_hibrido_integrado_nao_rodeRotas(requests_mock, travels_mock, mock_is_integrado):
    travels_mock[0].grupo.modelo_venda = Grupo.ModeloVenda.HIBRIDO
    travels_mock[0].grupo.save()
    resp = rodoviaria_svc.has_bpe(travels_mock[0])
    assert resp is None


def test_has_bpe_hibrido_nao_integrado(requests_mock, travels_mock):
    travels_mock[0].grupo.modelo_venda = Grupo.ModeloVenda.HIBRIDO
    travels_mock[0].grupo.save()
    with mock.patch("core.service.rodoviaria_svc._is_integrado", return_value=False):
        resp = rodoviaria_svc.has_bpe(travels_mock[0])
    assert resp is None


def test_has_bpe_hibrido_integrado_rodeRotas(requests_mock, travels_mock, mock_is_integrado, globalsettings_mock):
    travels_mock[0].grupo.modelo_venda = Grupo.ModeloVenda.HIBRIDO
    travels_mock[0].grupo.rotina_onibus = baker.make(RotinaOnibus)
    travels_mock[0].grupo.save()
    globalsettings_mock("rotinas_hibrido_integradas_rode_rotas", [travels_mock[0].grupo.rotina_onibus_id, 10])
    mocked_response = {"has_bpe": True}
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/has_bpe",
        json=mocked_response,
        status_code=200,
    )
    resp = rodoviaria_svc.has_bpe(travels_mock[0])
    assert resp is not None
    assert resp["has_bpe"] is True


def test_view_has_bpe_errors(client, user, travels_mock):
    user.id += 1
    user.username = "tony stark"
    user.save()
    client.force_login(user)
    resp = client.post(f"/api/reserva/hasbpe/{travels_mock[0].reservation_code}")
    assert resp.status_code == 200
    assert resp.json() is False
    resp = client.post(f"/api/reserva/hasbpe/{travels_mock[0].reservation_code[::-1]}")
    assert resp.status_code == 404


def test_efetua_compra(mocker, travels_mock):
    comprar_form, efetua_compra_response = _efetua_compra_mock(travels_mock)
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_compra_rodoviaria") as efetua_compra_mock,
    ):
        efetua_compra_mock.return_value = efetua_compra_response
        rodoviaria_svc.efetua_compra_unica(comprar_form)
        assert efetua_compra_mock.call_count == 1
        efetua_compra_mock.assert_called_with(
            1,  # trechoclasse_id
            travels_mock[0].id,  # travel_id
            [1],  # poltronas
            travels_mock[0].max_split_value,  # max_split_value
            [  # passageiros
                {
                    "id": 1,
                    "name": "Zezinho",
                    "cpf": None,
                    "buyer_cpf": None,
                    "rg_number": None,
                    "rg_orgao": None,
                    "tipo_documento": None,
                    "phone": "*********",
                    "birthday": date(1950, 10, 10),
                    "dados_beneficio": None,
                }
            ],
            comprar_form.categoria_especial,
            comprar_form.extra_poltronas,
            timeout=mocker.ANY,
        )


def test_remaneja_passageiro_hibrido(
    passageiro_mock,
    requests_mock,
    travels_mock,
    trechoclasse_mock,
):
    travel = travels_mock[0]
    travel_destino = travels_mock[1]
    with (
        mock.patch("core.service.rodoviaria_svc._is_integrado_marketplace_or_hibrido_roderotas") as mock_is_integrado,
        mock.patch("core.service.rodoviaria_svc._is_integrado_and_hibrido") as mock_is_integrado_and_hibrido,
        mock.patch(
            "core.service.rodoviaria_client.RodoviariaDeprecatedClient.add_pax_na_lista_batch"
        ) as mock_add_pax_na_lista_batch,
    ):
        mock_is_integrado.return_value = False
        mock_is_integrado_and_hibrido.return_value = True
        params = SimpleNamespace(travel=travel, travel_remanejada=travel_destino)
        rodoviaria_svc.remaneja_passageiros_hibrido_batch([params])
        mock_add_pax_na_lista_batch.assert_called_once_with(
            {
                "passageiros": [
                    {
                        "grupo_id": travel_destino.grupo_id,
                        "trechoclasse_id": travel_destino.trecho_classe_id,
                        "travel_id": travel_destino.id,
                        "valor_por_buseiro": str(travel_destino.max_split_value),
                        "passenger": {
                            "id": passageiro_mock.id,
                            "buseiro_id": passageiro_mock.buseiro_id,
                            "name": passageiro_mock.buseiro.name,
                            "cpf": None,
                            "rg_number": passageiro_mock.buseiro.rg_number,
                            "phone": None,
                            "tipo_documento": None,
                        },
                        "id_origem": travel_destino.trecho_classe.trecho_vendido.origem_id,
                        "id_destino": travel_destino.trecho_classe.trecho_vendido.destino_id,
                    }
                ]
            }
        )


def test_remaneja_passageiro(
    mocker,
    requests_mock,
    travels_mock,
    passageiro_mock,
    trechoclasse_mock,
):
    travel = travels_mock[1]
    travel_destino = travels_mock[0]
    travel_destino.user.profile = baker.prepare(Profile, cpf="*********01")
    travel_destino.reserva = baker.make(Reserva)

    mocker.patch("core.service.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch.object(
        MarketplaceSeatsController,
        "escolhe_e_bloqueia_poltronas",
        return_value=[
            BlockedSeat(
                poltrona=Assento(numero=1, x=0, y=0, tipo_assento="any"),
                tempo_limite_bloqueio=now() + timedelta(hours=24),
            )
        ],
    )
    mocker.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento")
    mocker.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "validate")

    params = dict(travel=travel, travel_destino=travel_destino)
    requests_mock.post(
        f"{settings.RODOVIARIA_API_URL}/v1/compra/comprar",
        json={
            "trechoclasse_id": travel_destino.trecho_classe_id,
            "travel_id": travel_destino.id,
            "poltronas": [1],
            "valor_cheio": 120.3,
            "passageiros": [
                {
                    "id": 684,
                    "name": passageiro_mock.buseiro.name,
                    "cpf": passageiro_mock.buseiro.cpf,
                    "buyer_cpf": "*********01",
                    "rg_number": passageiro_mock.buseiro.rg_number,
                    "rg_orgao": passageiro_mock.buseiro.rg_orgao,
                    "tipo_documento": None,
                    "phone": None,
                    "birthday": None,
                    "dados_beneficio": None,
                }
            ],
            "categoria_especial": "normal",
            "extra_poltronas": None,
        },
        status_code=200,
    )
    requests_mock.post(
        f"{settings.RODOVIARIA_API_URL}/v1/compra/cancela",
        json={
            "travel_id": travel.id,
            "buseiro_id": passageiro_mock.buseiro_id,
            "pax_valido": None,
        },
        status_code=200,
    )
    rodoviaria_reserva_svc.remaneja_passageiro(**params)


def test_remaneja_passageiros_async(
    requests_mock,
    mock_is_integrado,
    travels_mock,
    trechoclasse_mock,
    globalsettings_mock,
):
    globalsettings_mock("rodoviaria_remanejamento_v2", True)
    buseiro_1 = baker.make(Buseiro)
    buseiro_2 = baker.make(Buseiro)
    travel = travels_mock[0]
    baker.make(Passageiro, buseiro=buseiro_1, travel=travel)
    baker.make(Passageiro, buseiro=buseiro_2, travel=travel)
    travel_destino = travels_mock[1]
    travel_destino.user.profile = baker.make(Profile, cpf="*********01")
    travel_destino.reserva = baker.make(Reserva)
    travel_destino.save()
    travel_destino.user.profile.save()
    trecho_classe_origem = baker.make(
        TrechoClasse, grupo__modelo_venda=Grupo.ModeloVenda.HIBRIDO, grupo__company=travel.grupo.company
    )
    travel.trecho_classe = trecho_classe_origem
    travel.save()
    trecho_classe_destino = travel_destino.trecho_classe
    remanejamentos = [SimpleNamespace(travel=travel, travel_remanejada=travel_destino)]
    rodoviaria_reserva_svc.remanejamento_async(trecho_classe_origem, trecho_classe_destino, remanejamentos)


def test_remaneja_passageiros_async_origem_integrada(
    mocker,
    requests_mock,
    mock_is_integrado,
    travels_mock,
    trechoclasse_mock,
    passageiro_mock,
    globalsettings_mock,
):
    globalsettings_mock("rodoviaria_remanejamento_v2", True)
    buseiro_1 = baker.make(Buseiro)
    buseiro_2 = baker.make(Buseiro)
    travel = travels_mock[0]
    baker.make(Passageiro, buseiro=buseiro_1, travel=travel)
    baker.make(Passageiro, buseiro=buseiro_2, travel=travel)
    trecho_classe_origem = baker.make(
        TrechoClasse, grupo__modelo_venda=Grupo.ModeloVenda.BUSER, grupo__company=travel.grupo.company
    )
    travel.trecho_classe = trecho_classe_origem
    travel_destino = travels_mock[1]
    travel_destino.user.profile = baker.make(Profile, cpf="*********01")
    travel_destino.reserva = baker.make(Reserva)
    travel_destino.save()
    travel_destino.user.profile.save()
    trecho_classe_destino = travel_destino.trecho_classe

    mocker.patch.object(
        MarketplaceSeatsController,
        "escolhe_e_bloqueia_poltronas",
        return_value=[
            BlockedSeat(
                poltrona=Assento(numero=1, x=0, y=0, tipo_assento="any"),
                tempo_limite_bloqueio=now() + timedelta(hours=24),
            )
        ],
    )
    mocker.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento")
    requests_mock.post(
        f"{settings.RODOVIARIA_API_URL}/v1/compra/cancela",
        json={
            "travel_id": travel.id,
            "buseiro_id": passageiro_mock.buseiro_id,
            "pax_valido": None,
        },
        status_code=200,
    )

    remanejamentos = [SimpleNamespace(travel=travel, travel_remanejada=travel_destino)]
    rodoviaria_reserva_svc.remanejamento_async(trecho_classe_origem, trecho_classe_destino, remanejamentos)


def test_remaneja_passageiros_async_destino_nao_integrada(
    mocker, requests_mock, mock_is_integrado, travels_mock, trechoclasse_mock, passageiro_mock, globalsettings_mock
):
    globalsettings_mock("rodoviaria_remanejamento_v2", True)
    buseiro_1 = baker.make(Buseiro)
    buseiro_2 = baker.make(Buseiro)
    travel = travels_mock[0]
    baker.make(Passageiro, buseiro=buseiro_1, travel=travel)
    baker.make(Passageiro, buseiro=buseiro_2, travel=travel)
    trecho_classe_origem = travel.trecho_classe
    trecho_classe_destino = baker.make(
        TrechoClasse, grupo__modelo_venda=Grupo.ModeloVenda.BUSER, grupo__company=travel.grupo.company
    )
    travel_destino = travels_mock[1]
    travel_destino.user.profile = baker.make(Profile, cpf="*********01")
    travel_destino.reserva = baker.make(Reserva)
    travel_destino.save()
    travel_destino.user.profile.save()
    travel_destino.trecho_classe = trecho_classe_destino

    mocker.patch.object(
        MarketplaceSeatsController,
        "escolhe_e_bloqueia_poltronas",
        return_value=[
            BlockedSeat(
                poltrona=Assento(numero=1, x=0, y=0, tipo_assento="any"),
                tempo_limite_bloqueio=now() + timedelta(hours=24),
            )
        ],
    )
    mocker.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento")
    requests_mock.post(
        f"{settings.RODOVIARIA_API_URL}/v1/compra/cancela",
        json={
            "travel_id": travel.id,
            "buseiro_id": passageiro_mock.buseiro_id,
            "pax_valido": None,
        },
        status_code=200,
    )

    remanejamentos = [SimpleNamespace(travel=travel, travel_remanejada=travel_destino)]
    rodoviaria_reserva_svc.remanejamento_async(trecho_classe_origem, trecho_classe_destino, remanejamentos)


def test_remaneja_passageiros_async_nao_integrado(
    travels_mock,
    trechoclasse_mock,
    globalsettings_mock,
):
    globalsettings_mock("rodoviaria_remanejamento_v2", True)
    travel = travels_mock[0]
    travel_destino = travels_mock[1]
    trecho_classe_origem = travel.trecho_classe
    trecho_classe_origem.grupo.modelo_venda = Grupo.ModeloVenda.BUSER
    trecho_classe_origem.grupo.save()
    trecho_classe_destino = travel_destino.trecho_classe
    trecho_classe_destino.grupo.modelo_venda = Grupo.ModeloVenda.BUSER
    trecho_classe_destino.grupo.save()
    remanejamentos = [SimpleNamespace(travel=travel, travel_remanejada=travel_destino)]
    with mock.patch("core.service.rodoviaria_svc._client.remanejamento_async") as mock_remanejamento_async:
        rodoviaria_reserva_svc.remanejamento_async(trecho_classe_origem, trecho_classe_destino, remanejamentos)
    mock_remanejamento_async.assert_not_called()


def test_remaneja_passageiro_overbooking(travels_mock, passageiro_mock, trechoclasse_mock):
    travel = travels_mock[1]
    travel_destino = travels_mock[0]
    travel_destino.user.profile = baker.prepare(Profile, cpf="*********01")
    travel_destino.reserva = baker.make(Reserva)
    with (
        mock.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento"),
        mock.patch("core.service.rodoviaria_svc._is_integrado") as mock_is_integrado,
        mock.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe"),
        mock.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "validate"),
        mock.patch.object(
            MarketplaceSeatsController, "escolhe_e_bloqueia_poltronas"
        ) as mock_escolhe_e_bloqueia_poltronas,
    ):
        mock_is_integrado.return_value = True
        mock_escolhe_e_bloqueia_poltronas.side_effect = RodoviariaOverbooking("Não há valgas suficientes", "error", 0)
        params = dict(travel=travel, travel_destino=travel_destino)
        with pytest.raises(RodoviariaOverbooking):
            rodoviaria_reserva_svc.remaneja_passageiro(**params)


def test_remaneja_passageiro_connection_error(travels_mock, passageiro_mock, trechoclasse_mock):
    travel = travels_mock[1]
    travel_destino = travels_mock[0]
    travel_destino.user.profile = baker.prepare(Profile, cpf="*********01")
    travel_destino.reserva = baker.make(Reserva)
    with (
        mock.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento"),
        mock.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe"),
        mock.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "validate"),
        mock.patch("core.service.rodoviaria_svc._is_integrado") as mock_is_integrado,
        mock.patch.object(
            MarketplaceSeatsController, "escolhe_e_bloqueia_poltronas"
        ) as mock_escolhe_e_bloqueia_poltronas,
    ):
        mock_is_integrado.return_value = True
        mock_escolhe_e_bloqueia_poltronas.side_effect = SeatSelectionConnectionError()
        params = dict(travel=travel, travel_destino=travel_destino)
        with pytest.raises(RodoviariaConnectionException):
            rodoviaria_reserva_svc.remaneja_passageiro(**params)


def test_remaneja_passageiro_viagem_bloqueada(travels_mock, passageiro_mock, trechoclasse_mock):
    travel = travels_mock[1]
    travel_destino = travels_mock[0]
    travel_destino.user.profile = baker.prepare(Profile, cpf="*********01")
    travel_destino.reserva = baker.make(Reserva)
    with (
        mock.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento"),
        mock.patch("core.service.rodoviaria_svc._is_integrado") as mock_is_integrado,
        mock.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe"),
        mock.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "validate"),
        mock.patch.object(
            MarketplaceSeatsController, "escolhe_e_bloqueia_poltronas"
        ) as mock_escolhe_e_bloqueia_poltronas,
    ):
        mock_is_integrado.return_value = True
        mock_escolhe_e_bloqueia_poltronas.side_effect = RodoviariaViagemIndisponivel()
        params = dict(travel=travel, travel_destino=travel_destino)
        with pytest.raises(RodoviariaViagemIndisponivel):
            rodoviaria_reserva_svc.remaneja_passageiro(**params)


def test_remaneja_passageiro_categoria_especial(mocker, travels_mock, passageiro_mock, trechoclasse_mock):
    travel = travels_mock[1]
    travel_destino = travels_mock[0]
    travel_destino.user.profile = baker.prepare(Profile, cpf="*********01")
    travel_destino.reserva = baker.make(Reserva)

    mocker.patch("core.service.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch.object(Reserva, "categoria_especial_info", return_value=(CategoriaEspecial.IDOSO_100, None))

    params = dict(travel=travel, travel_destino=travel_destino)
    with pytest.raises(RodoviariaException, match="Reserva de categoria especial não pode ser remanejada"):
        rodoviaria_reserva_svc.remaneja_passageiro(**params)


def test_remaneja_passageiro_v2(mocker, travels_mock, passageiro_mock, trechoclasse_mock):
    travel = travels_mock[1]
    travel_destino = travels_mock[0]
    travel_destino.user.profile = baker.prepare(Profile, cpf="*********01")
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    mocker.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "validate")
    mocker.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "ensure_blocked_seat")
    mocker.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "build_request_form")
    mocker.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento")
    mocker.patch("core.service.rodoviaria_svc._is_integrado", return_value=True)
    mock_emissao = mocker.patch("core.service.rodoviaria_svc.efetua_compra_unica", return_value={})
    mock_cancelamento = mocker.patch("core.service.rodoviaria_svc.efetua_cancelamento")
    rodoviaria_reserva_svc.remaneja_passageiro(travel, travel_destino)
    mock_emissao.assert_called_once()
    mock_cancelamento.assert_called_once()


def test_remaneja_passageiro_v2_overbooking(mocker, travels_mock, passageiro_mock, trechoclasse_mock):
    travel = travels_mock[1]
    travel_destino = travels_mock[0]
    travel_destino.user.profile = baker.prepare(Profile, cpf="*********01")
    mocker.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento")
    mocker.patch("core.service.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    mocker.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "validate")
    mock_emissao = mocker.patch.object(
        rodoviaria_reserva_svc.EmissaoMarketplace,
        "emit",
        side_effect=RodoviariaOverbooking("Sem vagas", "error", 0),
    )
    mock_cancelamento = mocker.patch("core.service.rodoviaria_svc.efetua_cancelamento")
    with pytest.raises(RodoviariaOverbooking):
        rodoviaria_reserva_svc.remaneja_passageiro(travel, travel_destino)
    mock_emissao.assert_called_once()
    mock_cancelamento.assert_not_called()


def test_remaneja_passageiro_v2_connection_error(mocker, travels_mock, passageiro_mock, trechoclasse_mock):
    travel = travels_mock[1]
    travel_destino = travels_mock[0]
    travel_destino.user.profile = baker.prepare(Profile, cpf="*********01")
    mocker.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento")
    mocker.patch("core.service.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    mocker.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "validate")
    mock_emissao = mocker.patch.object(
        rodoviaria_reserva_svc.EmissaoMarketplace,
        "emit",
        side_effect=RodoviariaConnectionException("Erro de conexão"),
    )
    mock_cancelamento = mocker.patch("core.service.rodoviaria_svc.efetua_cancelamento")
    with pytest.raises(RodoviariaConnectionException, match="Erro de conexão"):
        rodoviaria_reserva_svc.remaneja_passageiro(travel, travel_destino)
    mock_emissao.assert_called_once()
    mock_cancelamento.assert_not_called()


def test_remaneja_passageiro_v2_blocked_travel(mocker, travels_mock, passageiro_mock, trechoclasse_mock):
    travel = travels_mock[1]
    travel_destino = travels_mock[0]
    travel_destino.user.profile = baker.prepare(Profile, cpf="*********01")
    mocker.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento")
    mocker.patch("core.service.rodoviaria_svc._is_integrado", return_value=True)
    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    mocker.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "validate")
    mock_emissao = mocker.patch.object(
        rodoviaria_reserva_svc.EmissaoMarketplace,
        "emit",
        side_effect=RodoviariaViagemIndisponivel("Viagem indisponivel"),
    )
    mock_cancelamento = mocker.patch("core.service.rodoviaria_svc.efetua_cancelamento")
    with pytest.raises(RodoviariaViagemIndisponivel):
        rodoviaria_reserva_svc.remaneja_passageiro(travel, travel_destino)
    mock_emissao.assert_called_once()
    mock_cancelamento.assert_not_called()


def test_remaneja_passageiro_v2_erro_cancelamento_travel_origem(
    mocker, travels_mock, passageiro_mock, trechoclasse_mock
):
    travel = travels_mock[1]
    travel_destino = travels_mock[0]
    travel_destino.user.profile = baker.prepare(Profile, cpf="*********01")
    mocker.patch("core.service.rodoviaria_svc._raise_for_antecedencia_cancelamento")
    mocker.patch("core.service.rodoviaria_svc.is_integrado_and_marketplace", return_value=[False, True])

    mocker.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    mocker.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "validate")

    mock_emissao = mocker.patch.object(rodoviaria_reserva_svc.EmissaoMarketplace, "emit")
    mock_cancelamento = mocker.patch(
        "core.service.rodoviaria_svc.efetua_cancelamento",
        side_effect=[RodoviariaException("Erro de cancelamento"), None],
    )
    with pytest.raises(RodoviariaException, match="Erro de cancelamento"):
        rodoviaria_reserva_svc.remaneja_passageiro(travel, travel_destino)
    mock_emissao.assert_called_once()
    assert mock_cancelamento.call_count == 2


def test_fetch_rotas(rf, user_staff, trechoclasse_mock, requests_mock):
    grupo_id = trechoclasse_mock.grupo_id
    mocked_response = {"rota": []}
    requests_mock.post(
        f"{settings.RODOVIARIA_API_URL}/v1/fetch_rotas",
        json=mocked_response,
        status_code=200,
    )
    resp = rodoviaria_svc.fetch_rotas(grupo_id=grupo_id)
    assert resp == mocked_response


def test_fetch_rotas_por_company(rf, user_staff, trechoclasse_mock, requests_mock):
    mocked_response = {"rota": []}
    with mock.patch(
        "core.service.rodoviaria_svc._client.fetch_rotas", return_value=mocked_response
    ) as mock_fetch_rotas:
        resp = rodoviaria_svc.fetch_rotas(
            company_id=trechoclasse_mock.grupo.company_id, modelo_venda=trechoclasse_mock.grupo.modelo_venda
        )
    mock_fetch_rotas.assert_called_once_with(
        trechoclasse_mock.grupo.company_id, trechoclasse_mock.grupo.modelo_venda, None
    )
    assert resp == mocked_response


def test_fetch_rotas_erro(rf, user_staff, trechoclasse_mock, requests_mock):
    grupo_id = trechoclasse_mock.grupo_id
    mocked_response = {"message": "Necessário passar o grupo_id ou company_id"}
    requests_mock.post(
        f"{settings.RODOVIARIA_API_URL}/v1/fetch_rotas",
        json=mocked_response,
        status_code=422,
    )
    resp = {}
    with pytest.raises(RodoviariaClientError, match="Falha na busca de rotas"):
        resp = rodoviaria_svc.fetch_rotas(grupo_id=grupo_id)
    assert resp == {}


def test_efetua_compra_erro(travels_mock):
    comprar_form, _ = _efetua_compra_mock(travels_mock)
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_compra_rodoviaria") as efetua_compra_mock,
        pytest.raises(
            RodoviariaConnectionException, match=r"Erro na conexão com a empresa parceira. \(connection_error\)"
        ),
    ):
        efetua_compra_mock.side_effect = [
            RodoviariaException(message="Erro na conexão com a empresa parceira.", type="connection_error"),
        ]
        rodoviaria_svc.efetua_compra_unica(comprar_form)
    assert efetua_compra_mock.call_count == 1


def test_efetua_compra_erro_viagem_bloqueada(travels_mock):
    comprar_form, efetua_compra_response = _efetua_compra_mock(travels_mock)
    exception_message = "Texto qualquer do rodoviaria bla bla bla"
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_compra_rodoviaria") as efetua_compra_mock,
        pytest.raises(RodoviariaViagemBloqueada) as ex,
    ):
        efetua_compra_mock.side_effect = RodoviariaException(message=exception_message, type="service_not_for_sale")
        rodoviaria_svc.efetua_compra_unica(comprar_form)

    assert ex.value.message == RodoviariaViagemBloqueada.message == "Essa viagem não está mais disponível"
    assert ex.value.help == RodoviariaViagemBloqueada.help == "Por favor, escolha outra viagem"
    assert ex.value.type == RodoviariaViagemBloqueada.type == "service_not_for_sale"
    assert efetua_compra_mock.call_count == 1


def test_efetua_compra_erro_too_many_requests(travels_mock):
    travels_rodoviaria, _ = _efetua_compra_mock(travels_mock)
    exception_message = "Totalbus erro 429 bla bla bla"
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_compra_rodoviaria") as efetua_compra_mock,
        pytest.raises(
            RodoviariaConnectionException,
            match=r"Erro na conexão com a empresa parceira. \(connection_error\)",
        ),
    ):
        efetua_compra_mock.side_effect = RodoviariaException(message=exception_message, type="connection_error")
        rodoviaria_svc.efetua_compra_unica(travels_rodoviaria)
    assert efetua_compra_mock.call_count == 1


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_remove_passageiro_not_blocking_error():
    travel = SimpleNamespace(
        id=5,
        trecho_classe_id=10,
        trecho_classe=SimpleNamespace(
            datetime_ida=timezone.now() + timedelta(hours=5), grupo=SimpleNamespace(company_id=23423423)
        ),
        status="pending",
    )
    passageiro = SimpleNamespace(buseiro_id=10, removed=False)
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as is_integrado_mock,
    ):
        is_integrado_mock.return_value = True
        efetua_cancelamento_mock.side_effect = RodoviariaNotBlockingException("erro")
        assert rodoviaria_svc.remove_passageiro(travel, passageiro) is None
        efetua_cancelamento_mock.assert_called_with(travel.id, passageiro.buseiro_id, True)


@mock.patch("core.service.log_svc._create_log")
def test_efetua_cancelamento_not_blocking_error(time_machine, mock_is_integrado):
    time_machine.move_to(to_default_tz_required("2022-01-10 14:32:00"))
    travel = baker.make(
        Travel,
        trecho_classe__datetime_ida=timezone.now() + timedelta(hours=5),
        trecho_classe__grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    with mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock:
        efetua_cancelamento_mock.side_effect = RodoviariaNotBlockingException("erro")
        response = rodoviaria_svc.efetua_cancelamento(travel)
        efetua_cancelamento_mock.assert_called_with(travel.id)
    assert response is None


@mock.patch("core.service.log_svc._create_log")
def test_efetua_cancelamento_connection_error(time_machine, mock_is_integrado):
    time_machine.move_to(to_default_tz_required("2022-01-10 14:32:00"))
    travel = baker.make(
        Travel,
        trecho_classe__datetime_ida=timezone.now() + timedelta(hours=5),
        trecho_classe__grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._client.solicitar_cancelamento") as solicitar_cancelamento_mock,
    ):
        efetua_cancelamento_mock.side_effect = RodoviariaConnectionException("erro de conexão")
        response = rodoviaria_svc.efetua_cancelamento(travel)
        efetua_cancelamento_mock.assert_called_with(travel.id)
    solicitar_cancelamento_mock.assert_called_once_with(travel.trecho_classe_id, travel.id)
    assert response is None


@mock.patch("core.service.log_svc._create_log")
def test_efetua_cancelamento_timeout(time_machine, mock_is_integrado):
    time_machine.move_to(to_default_tz_required("2022-01-10 14:32:00"))
    travel = baker.make(
        Travel,
        trecho_classe__datetime_ida=timezone.now() + timedelta(hours=5),
        trecho_classe__grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._client.solicitar_cancelamento") as solicitar_cancelamento_mock,
    ):
        efetua_cancelamento_mock.side_effect = requests.ReadTimeout
        response = rodoviaria_svc.efetua_cancelamento(travel)
        efetua_cancelamento_mock.assert_called_with(travel.id)
    solicitar_cancelamento_mock.assert_called_once_with(travel.trecho_classe_id, travel.id)
    assert response is None


@mock.patch("core.service.log_svc._create_log")
def test_efetua_cancelamento_reserva_nao_paga(time_machine, mock_is_integrado):
    time_machine.move_to(to_default_tz_required("2022-01-10 14:32:00"))
    travel = baker.make(
        Travel,
        trecho_classe__datetime_ida=timezone.now() + timedelta(hours=5),
        trecho_classe__grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )

    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._client.solicitar_cancelamento") as solicitar_cancelamento_mock,
    ):
        efetua_cancelamento_mock.side_effect = RodoviariaException("erro")
        response = rodoviaria_svc.efetua_cancelamento(travel, cancel_strategy_type="BOLETO_UNPAID")

    efetua_cancelamento_mock.assert_called_with(travel.id)
    solicitar_cancelamento_mock.assert_called_once_with(travel.trecho_classe_id, travel.id)
    assert response is None


def test_fechar_grupo_rodoviaria(mock_is_integrado):
    horario_inicio_teste = timezone.now()
    company = baker.make("core.Company")
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.HIBRIDO, company=company)
    grupo_classe = baker.make("core.GrupoClasse", grupo=grupo)
    grupos_classe_to_update = []
    with mock.patch(
        "core.service.rodoviaria_svc._client.fechar_grupos_classe_hibridos"
    ) as mock_fechar_grupos_classe_hibridos:
        response = rodoviaria_svc.fechar_grupo_rodoviaria(grupo, grupos_classe_to_update=grupos_classe_to_update)
    grupos_classe_to_update[0].__class__.objects.bulk_update(
        grupos_classe_to_update, ["closed", "closed_reason", "closed_at"]
    )
    mock_fechar_grupos_classe_hibridos.assert_called_once_with(company.id, [grupo_classe.id])
    assert response == mock_fechar_grupos_classe_hibridos.return_value
    grupo_classe.refresh_from_db()
    assert grupo_classe.closed
    assert grupo_classe.closed_reason == "Grupo híbrido com ônibus de fretamento"
    assert grupo_classe.closed_at > horario_inicio_teste


def test_emitir_passagens_pendentes():
    company = baker.make("core.Company")
    grupo = baker.make("core.Grupo", company=company)
    origem = baker.make("core.LocalEmbarque")
    destino = baker.make("core.LocalEmbarque")
    trecho_vendido = baker.make("core.TrechoVendido", origem=origem, destino=destino)
    trecho_classe = baker.make("core.TrechoClasse", trecho_vendido=trecho_vendido)
    travel = baker.make("core.Travel", grupo=grupo, trecho_classe=trecho_classe, max_split_value=65)
    buseiros = (
        baker.make(
            "core.Buseiro",
            name="Passageiro 1",
            phone="12988776655",
            cpf="56631603021",
            rg_number="*********",
            _fill_optional=["user"],
        ),
        baker.make("core.Buseiro", rg_number="*********", _fill_optional=["user"]),
    )
    buseiro_1, buseiro_2 = buseiros
    for buseiro in buseiros:
        baker.make("core.Profile", user_id=buseiro.user_id)
    passageiros_grupo = (
        baker.make("core.Passageiro", travel=travel, buseiro=buseiro_1),  # passagem pendente
        baker.make("core.Passageiro", travel=travel, buseiro=buseiro_2),  # passagem confirmada na rodoviaria
    )
    with (
        mock.patch("core.service.rodoviaria_svc._client.get_passageiros") as mock_get_passageiros,
        mock.patch("core.service.rodoviaria_svc.add_pax_na_lista") as mock_add_pax_na_lista,
    ):
        mock_get_passageiros.return_value = [{"buseiro_id": buseiro_2.id, "travel_id": travel.id}]
        rodoviaria_svc.emitir_passagens_pendentes(grupo, passageiros_grupo=passageiros_grupo)
    mock_get_passageiros.assert_called_once_with([travel.id])
    mock_add_pax_na_lista.assert_called_once_with(
        CheckPaxForm.parse_obj(
            {
                "trechoclasseId": trecho_classe.id,
                "travelId": travel.id,
                "valorPorBuseiro": 65,
                "passenger": {
                    "buseiroId": buseiro_1.id,
                    "name": "Passageiro 1",
                    "cpf": "56631603021",
                    "rgNumber": "*********",
                    "phone": "12988776655",
                    "buyer_cpf": "",
                },
                "idOrigem": origem.id,
                "idDestino": destino.id,
            }
        ).dict(),
        listar_passageiros=False,
    )


def test_add_pax_na_lista_por_passageiro():
    origem = baker.make("core.LocalEmbarque")
    destino = baker.make("core.LocalEmbarque")
    trecho_vendido = baker.make("core.TrechoVendido", origem=origem, destino=destino)
    trecho_classe = baker.make("core.TrechoClasse", trecho_vendido=trecho_vendido)
    travel = baker.make("core.Travel", trecho_classe=trecho_classe, max_split_value=65)
    buseiro = baker.make(
        "core.Buseiro",
        name="Passageiro 1",
        phone="12988776655",
        cpf="56631603021",
        rg_number="*********",
        user__profile__cpf="56631603021",
    )
    passageiro = baker.make("core.Passageiro", travel=travel, buseiro=buseiro)
    with mock.patch("core.service.rodoviaria_svc.add_pax_na_lista") as mock_add_pax_na_lista:
        rodoviaria_svc.add_pax_na_lista_por_passageiro(passageiro)
    mock_add_pax_na_lista.assert_called_once_with(
        CheckPaxForm.parse_obj(
            {
                "trechoclasseId": trecho_classe.id,
                "travelId": travel.id,
                "valorPorBuseiro": 65,
                "passenger": {
                    "buseiroId": buseiro.id,
                    "name": "Passageiro 1",
                    "cpf": "56631603021",
                    "rgNumber": "*********",
                    "phone": "12988776655",
                    "buyer_cpf": "56631603021",
                },
                "idOrigem": origem.id,
                "idDestino": destino.id,
            }
        ).dict(),
        listar_passageiros=False,
    )


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_remove_passageiro_menos_3h_para_embarque():
    travel = baker.make(
        "core.Travel",
        trecho_classe=baker.make("core.TrechoClasse", datetime_ida=timezone.now() + timedelta(hours=2)),
    )
    passageiro = SimpleNamespace(buseiro_id=10)
    with (
        mock.patch("core.service.rodoviaria_svc._is_integrado") as is_integrado_mock,
        pytest.raises(
            RodoviariaCancelamentoException,
            match="Passagem só pode ser cancelada no sistema do parceiro até 3h antes da partida",
        ),
        mock.patch(
            "core.service.rodoviaria_svc._client.get_passageiros",
            return_value=["passagem_confirmada"],
        ),
    ):
        is_integrado_mock.return_value = True
        rodoviaria_svc.remove_passageiro(travel, passageiro)


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_remove_passageiro_menos_3h_para_embarque_passagens_canceladas():
    travel = baker.make(
        "core.Travel",
        trecho_classe=baker.make("core.TrechoClasse", datetime_ida=timezone.now() + timedelta(hours=2)),
    )
    passageiro = SimpleNamespace(buseiro_id=10, removed=False)
    with (
        mock.patch("core.service.rodoviaria_svc._is_integrado") as is_integrado_mock,
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as mock_efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._client.get_passageiros", return_value=[]),
    ):
        is_integrado_mock.return_value = True
        rodoviaria_svc.remove_passageiro(travel, passageiro)
    mock_efetua_cancelamento_mock.assert_called_once()


@pytest.mark.parametrize(
    "grupo_modelo_venda,diff_trecho_hours_from_now,is_integrado",
    [
        pytest.param(
            Grupo.ModeloVenda.MARKETPLACE,
            2,
            True,
            id="integrado_menos_3h",
        ),
        pytest.param(
            Grupo.ModeloVenda.MARKETPLACE,
            2,
            False,
            id="manual_menos_3h",
        ),
        pytest.param(
            Grupo.ModeloVenda.HIBRIDO,
            2,
            True,
            id="hibrido_menos_3h",
        ),
    ],
)
@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_efetua_cancelamento_menos_3h_para_embarque(grupo_modelo_venda, diff_trecho_hours_from_now, is_integrado):
    travel = baker.make(
        "core.Travel",
        trecho_classe=baker.make(
            "core.TrechoClasse",
            datetime_ida=timezone.now() + timedelta(hours=diff_trecho_hours_from_now),
            grupo=baker.make(Grupo, modelo_venda=grupo_modelo_venda),
        ),
    )
    with (
        mock.patch("core.service.rodoviaria_svc._is_prod") as _is_prod_mock,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as is_integrado_mock,
        mock.patch(
            "core.service.rodoviaria_svc._client.get_passageiros",
            return_value=["passagem_confirmada"],
        ),
    ):
        with pytest.raises(
            RodoviariaCancelamentoException,
            match="Passagem só pode ser cancelada no sistema do parceiro até 3h antes da partida",
        ):
            _is_prod_mock.return_value = True
            is_integrado_mock.return_value = is_integrado
            rodoviaria_svc.efetua_cancelamento(travel)


def test_atualiza_precos_e_vagas_rodoviaria(requests_mock, trechoclasse_mock):
    trechoclasse_mock.datetime_ida = timezone.now() + timedelta(hours=1)
    trechoclasse_mock.save()
    new_preco_rodoviaria = 100.0
    new_vagas = 10
    mocked_get_rodoviaria_trechos_classe_response = {
        "trechos": [
            {
                "id": 1,
                "company_name": "Expresso Adamantina",
                "integracao": "totalbus",
                "company_internal_id": 1,
                "trechoclasse_internal_id": trechoclasse_mock.id,
                "tags": ["to_be_updated"],
                "datetime_ida": "2021-10-12 10:32 LMT",
                "grupo_internal_id": 1,
                "grupo_datetime_ida": "2021-10-12 10:32 LMT",
                "origem_internal_id": 1,
                "origem_nickname": "Local A",
                "origem_cidade": "Cidade A",
                "destino_internal_id": 2,
                "destino_nickname": "Local B",
                "destino_cidade": "Cidade B",
                "preco_rodoviaria": new_preco_rodoviaria,
                "vagas": new_vagas,
                "active": True,
            }
        ]
    }
    mocked_remove_trechos_classe_tags = {"mensagem": "As tags foram removidas com sucesso."}
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/trechoclasse/get",
        json=mocked_get_rodoviaria_trechos_classe_response,
        status_code=200,
    )
    requests_mock.post(
        f"{settings.RODOVIARIA_API_URL}/v1/trechoclasse/remove_tag",
        json=mocked_remove_trechos_classe_tags,
        status_code=200,
    )
    resp = rodoviaria_svc.update_trechos_classe_data(tag="to_be_updated")
    trechoclasse_mock.refresh_from_db()
    trechoclasse_mock.trecho_vendido.refresh_from_db()
    assert resp["get_trechos_classe_response"]["trechos"][0]["vagas"] == new_vagas
    assert resp["get_trechos_classe_response"]["trechos"][0]["preco_rodoviaria"] == new_preco_rodoviaria
    assert len(resp["get_trechos_classe_response"]["trechos"]) == 1
    assert resp["remove_tags_response"]["mensagem"] == "As tags foram removidas com sucesso."
    assert trechoclasse_mock.trecho_vendido.preco_rodoviaria == new_preco_rodoviaria
    assert trechoclasse_mock.max_split_value == new_preco_rodoviaria
    assert trechoclasse_mock.ref_split_value == new_preco_rodoviaria
    assert trechoclasse_mock.vagas == new_vagas


def test_atualiza_precos_e_vagas_rodoviaria_nao_atualiza_vagas_se_for_none(requests_mock, trechoclasse_mock):
    trechoclasse_mock.datetime_ida = timezone.now() + timedelta(hours=1)
    trechoclasse_mock.vagas = 10
    trechoclasse_mock.save()
    new_preco_rodoviaria = 100.0
    new_vagas = None
    mocked_get_rodoviaria_trechos_classe_response = {
        "trechos": [
            {
                "id": 1,
                "company_name": "Expresso Adamantina",
                "integracao": "totalbus",
                "company_internal_id": 1,
                "trechoclasse_internal_id": trechoclasse_mock.id,
                "tags": ["to_be_updated"],
                "datetime_ida": "2021-10-12 10:32 LMT",
                "grupo_internal_id": 1,
                "grupo_datetime_ida": "2021-10-12 10:32 LMT",
                "origem_internal_id": 1,
                "origem_nickname": "Local A",
                "origem_cidade": "Cidade A",
                "destino_internal_id": 2,
                "destino_nickname": "Local B",
                "destino_cidade": "Cidade B",
                "preco_rodoviaria": new_preco_rodoviaria,
                "vagas": new_vagas,
                "active": True,
            }
        ]
    }
    mocked_remove_trechos_classe_tags = {"mensagem": "As tags foram removidas com sucesso."}
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/trechoclasse/get",
        json=mocked_get_rodoviaria_trechos_classe_response,
        status_code=200,
    )
    requests_mock.post(
        f"{settings.RODOVIARIA_API_URL}/v1/trechoclasse/remove_tag",
        json=mocked_remove_trechos_classe_tags,
        status_code=200,
    )
    resp = rodoviaria_svc.update_trechos_classe_data(tag="to_be_updated")
    trechoclasse_mock.refresh_from_db()
    trechoclasse_mock.trecho_vendido.refresh_from_db()
    assert resp["get_trechos_classe_response"]["trechos"][0]["vagas"] == new_vagas
    assert resp["get_trechos_classe_response"]["trechos"][0]["preco_rodoviaria"] == new_preco_rodoviaria
    assert len(resp["get_trechos_classe_response"]["trechos"]) == 1
    assert resp["remove_tags_response"]["mensagem"] == "As tags foram removidas com sucesso."
    assert trechoclasse_mock.trecho_vendido.preco_rodoviaria == new_preco_rodoviaria
    assert trechoclasse_mock.max_split_value == new_preco_rodoviaria
    assert trechoclasse_mock.ref_split_value == new_preco_rodoviaria
    assert trechoclasse_mock.vagas == 10


def test_atualiza_precos_e_vagas_rodoviaria_no_tag(requests_mock, trechoclasse_mock):
    trechoclasse_mock.datetime_ida = timezone.now() + timedelta(hours=1)
    trechoclasse_mock.save()
    new_preco_rodoviaria = 100.0
    new_vagas = 10
    mocked_get_rodoviaria_trechos_classe_response = {
        "trechos": [
            {
                "id": 1,
                "company_name": "Expresso Adamantina",
                "integracao": "totalbus",
                "company_internal_id": 1,
                "trechoclasse_internal_id": trechoclasse_mock.id,
                "tags": ["to_be_updated"],
                "datetime_ida": "2021-10-12 10:32 LMT",
                "grupo_internal_id": 1,
                "grupo_datetime_ida": "2021-10-12 10:32 LMT",
                "origem_internal_id": 1,
                "origem_nickname": "Local A",
                "origem_cidade": "Cidade A",
                "destino_internal_id": 2,
                "destino_nickname": "Local B",
                "destino_cidade": "Cidade B",
                "preco_rodoviaria": new_preco_rodoviaria,
                "vagas": new_vagas,
                "active": True,
            }
        ]
    }
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/trechoclasse/get",
        json=mocked_get_rodoviaria_trechos_classe_response,
        status_code=200,
    )
    resp = rodoviaria_svc.update_trechos_classe_data(trechoclasse_ids=f"{trechoclasse_mock.id}")
    trechoclasse_mock.refresh_from_db()
    trechoclasse_mock.trecho_vendido.refresh_from_db()
    assert resp["get_trechos_classe_response"]["trechos"][0]["preco_rodoviaria"] == new_preco_rodoviaria
    assert resp["get_trechos_classe_response"]["trechos"][0]["vagas"] == new_vagas
    assert len(resp["get_trechos_classe_response"]["trechos"]) == 1
    assert resp.get("remove_tags_response") is None
    assert trechoclasse_mock.trecho_vendido.preco_rodoviaria == new_preco_rodoviaria
    assert trechoclasse_mock.max_split_value == new_preco_rodoviaria
    assert trechoclasse_mock.ref_split_value == new_preco_rodoviaria
    assert trechoclasse_mock.vagas == new_vagas


def test_atualiza_precos_e_vagas_rodoviaria_fecha_trecho_sem_vagas(requests_mock, trechoclasse_mock):
    trechoclasse_mock.datetime_ida = timezone.now() + timedelta(hours=1)
    trechoclasse_mock.save()
    new_preco_rodoviaria = 100.0
    new_vagas = 0
    mocked_get_rodoviaria_trechos_classe_response = {
        "trechos": [
            {
                "id": 1,
                "company_name": "Expresso Adamantina",
                "integracao": "totalbus",
                "company_internal_id": 1,
                "trechoclasse_internal_id": trechoclasse_mock.id,
                "tags": ["to_be_updated"],
                "datetime_ida": "2021-10-12 10:32 LMT",
                "grupo_internal_id": 1,
                "grupo_datetime_ida": "2021-10-12 10:32 LMT",
                "origem_internal_id": 1,
                "origem_nickname": "Local A",
                "origem_cidade": "Cidade A",
                "destino_internal_id": 2,
                "destino_nickname": "Local B",
                "destino_cidade": "Cidade B",
                "preco_rodoviaria": new_preco_rodoviaria,
                "vagas": new_vagas,
                "active": True,
            }
        ]
    }
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/trechoclasse/get",
        json=mocked_get_rodoviaria_trechos_classe_response,
        status_code=200,
    )
    resp = rodoviaria_svc.update_trechos_classe_data(trechoclasse_ids=f"{trechoclasse_mock.id}")
    trechoclasse_mock.refresh_from_db()
    trechoclasse_mock.trecho_vendido.refresh_from_db()
    assert resp["get_trechos_classe_response"]["trechos"][0]["preco_rodoviaria"] == new_preco_rodoviaria
    assert resp["get_trechos_classe_response"]["trechos"][0]["vagas"] == new_vagas
    assert len(resp["get_trechos_classe_response"]["trechos"]) == 1
    assert resp.get("remove_tags_response") is None
    assert trechoclasse_mock.trecho_vendido.preco_rodoviaria == new_preco_rodoviaria
    assert trechoclasse_mock.max_split_value == new_preco_rodoviaria
    assert trechoclasse_mock.ref_split_value == new_preco_rodoviaria
    assert trechoclasse_mock.vagas == new_vagas
    assert trechoclasse_mock.closed


def test_atualiza_precos_e_vagas_rodoviaria_reabre_trecho(requests_mock, trechoclasse_mock):
    trechoclasse_mock.datetime_ida = timezone.now() + timedelta(hours=1)
    trechoclasse_mock.closed = True
    trechoclasse_mock.closed_reason = RodoviariaTags.Closed.NO_SEATS_AVAILABLE
    trechoclasse_mock.save()
    new_preco_rodoviaria = 100.0
    new_vagas = 10
    mocked_get_rodoviaria_trechos_classe_response = {
        "trechos": [
            {
                "id": 1,
                "company_name": "Expresso Adamantina",
                "integracao": "totalbus",
                "company_internal_id": 1,
                "trechoclasse_internal_id": trechoclasse_mock.id,
                "tags": ["to_be_updated"],
                "datetime_ida": "2021-10-12 10:32 LMT",
                "grupo_internal_id": 1,
                "grupo_datetime_ida": "2021-10-12 10:32 LMT",
                "origem_internal_id": 1,
                "origem_nickname": "Local A",
                "origem_cidade": "Cidade A",
                "destino_internal_id": 2,
                "destino_nickname": "Local B",
                "destino_cidade": "Cidade B",
                "preco_rodoviaria": new_preco_rodoviaria,
                "vagas": new_vagas,
                "active": True,
            }
        ]
    }
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/trechoclasse/get",
        json=mocked_get_rodoviaria_trechos_classe_response,
        status_code=200,
    )
    resp = rodoviaria_svc.update_trechos_classe_data(trechoclasse_ids=f"{trechoclasse_mock.id}")
    trechoclasse_mock.refresh_from_db()
    trechoclasse_mock.trecho_vendido.refresh_from_db()
    assert resp["get_trechos_classe_response"]["trechos"][0]["preco_rodoviaria"] == new_preco_rodoviaria
    assert resp["get_trechos_classe_response"]["trechos"][0]["vagas"] == new_vagas
    assert len(resp["get_trechos_classe_response"]["trechos"]) == 1
    assert resp.get("remove_tags_response") is None
    assert trechoclasse_mock.trecho_vendido.preco_rodoviaria == new_preco_rodoviaria
    assert trechoclasse_mock.max_split_value == new_preco_rodoviaria
    assert trechoclasse_mock.ref_split_value == new_preco_rodoviaria
    assert trechoclasse_mock.vagas == new_vagas
    assert not trechoclasse_mock.closed


def test_atualiza_precos_e_vagas_rodoviaria_no_param(requests_mock):
    mocked_get_rodoviaria_trechos_classe_response = {
        "error": "É necessário algum parâmetro tag, integracao, company_id, grupo_id ou trechoclasse_ids"
    }
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/trechoclasse/get",
        json=mocked_get_rodoviaria_trechos_classe_response,
        status_code=400,
    )
    resp = rodoviaria_svc.update_trechos_classe_data()
    assert resp["error"] == "É necessário algum parâmetro tag, integracao, company_id, grupo_id ou trechoclasse_ids"


def test_solicitar_cancelamento_travels_grupo():
    grupo = baker.make("core.Grupo")
    trecho_classe = baker.make("core.TrechoClasse", grupo=grupo)
    travels = baker.make("core.Travel", trecho_classe=trecho_classe, _quantity=4)
    with (
        mock.patch("core.service.rodoviaria_svc._is_integrado") as mock_is_integrado,
        mock.patch(
            "core.service.rodoviaria_svc._client.solicitar_cancelamento_travels"
        ) as mock_solicitar_cancelamento_travels,
    ):
        mock_is_integrado.return_value = True
        rodoviaria_svc.solicitar_cancelamento_travels_grupo(grupo, travels)
    mock_solicitar_cancelamento_travels.assert_called_once_with([t.id for t in travels])


def test_get_status_integracao_grupo():
    status_rodoviaria_response = {"1": {"status": "integracao_ok", "preco_rodoviaria": 139.9}}
    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.get_trecho_classe_integracao"
        ) as mock_get_trecho_classe_integracao,
        mock.patch(
            "core.service.rodoviaria_svc.check_status_integracao_trechos"
        ) as mock_check_status_integracao_trechos,
        mock.patch("core.service.rodoviaria_svc._get_map_trechos") as mock_get_map_trechos,
    ):
        mock_get_trecho_classe_integracao.return_value = status_rodoviaria_response
        mock_get_map_trechos.return_value = {20: baker.make("core.TrechoClasse", id=20)}
        rodoviaria_svc.get_status_integracao_grupo(grupo_id=10, trecho_classe_id=20)
    mock_get_trecho_classe_integracao.assert_called_once_with(10, 20)
    mock_get_map_trechos.assert_called_once_with(10, 20)
    mock_check_status_integracao_trechos.assert_called_once_with(
        status_rodoviaria_response,
        map_trechos_classe=mock_get_map_trechos.return_value,
        gatilho_atualizacao=TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL,
        atualiza_trechos=False,
    )


def test_update_status_integracao_grupo():
    status_rodoviaria_response = {"message": "Os links estão sendo atualizados"}
    with mock.patch(
        "core.service.rodoviaria_svc._client.update_trecho_classe_integracao"
    ) as mock_update_trecho_classe_integracao:
        mock_update_trecho_classe_integracao.return_value = status_rodoviaria_response
        response = rodoviaria_svc.update_status_integracao_grupo(grupo_id=10)
    mock_update_trecho_classe_integracao.assert_called_once_with(grupo_id=10)
    assert response == status_rodoviaria_response


def test_update_status_integracao_trecho_classe():
    trecho_classe = baker.make(
        "core.TrechoClasse", grupo=baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    )
    status_rodoviaria_response = {str(trecho_classe.id): {"status": "integracao_ok", "preco_rodoviaria": 139.9}}
    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.update_trecho_classe_integracao"
        ) as mock_update_trecho_classe_integracao,
        mock.patch(
            "core.service.rodoviaria_svc.check_status_integracao_trechos"
        ) as mock_check_status_integracao_trechos,
    ):
        mock_update_trecho_classe_integracao.return_value = status_rodoviaria_response
        rodoviaria_svc.update_status_integracao_trecho_classe(trecho_classe)
    mock_update_trecho_classe_integracao.assert_called_once_with(trecho_classe_id=trecho_classe.id)
    mock_check_status_integracao_trechos.assert_called_once_with(
        status_rodoviaria_response,
        map_trechos_classe={trecho_classe.id: trecho_classe},
        gatilho_atualizacao=TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL,
        atualiza_trechos=True,
    )


def test_update_trecho_grupo_update_vagas_nao_abre_trecho_fechado_sem_vagas():
    tc = baker.make(
        "core.TrechoClasse",
        max_split_value=D("139.90"),
        vagas=7,
        closed=True,
        closed_reason=RodoviariaTags.Closed.NO_SEATS_AVAILABLE,
    )
    _, status_details = rodoviaria_svc.update_trecho_grupo_update_vagas(
        tc, 0, TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_CHECKOUT
    )
    assert "(trecho fechado)" in status_details


def test_update_trecho_grupo_update_vagas_atualiza():
    tc = baker.make(
        "core.TrechoClasse",
        max_split_value=D("139.90"),
        datetime_ida=timezone.now(),
        vagas=7,
        closed=True,
        closed_reason=RodoviariaTags.Closed.NO_SEATS_AVAILABLE,
    )
    _, status_details = rodoviaria_svc.update_trecho_grupo_update_vagas(
        tc, 3, TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_CHECKOUT, save=True
    )
    tc.refresh_from_db()
    assert tc.vagas == 3


def test_check_status_integracao_trechos_integracao_ok_nao_atualiza_trecho(mocker):
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.HIBRIDO)
    mocker.patch.object(
        RodoviariaClient,
        "get_company_info",
        return_value={
            "company_internal_id": grupo.company_id,
            "modelo_venda": grupo.modelo_venda,
            "features": [],
        },
    )
    tc = baker.make("core.TrechoClasse", max_split_value=D("139.90"), grupo=grupo)
    status_rodoviaria = {str(tc.id): {"status": "integracao_ok", "preco_rodoviaria": 139.9, "vagas": 5}}
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(
        status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, False
    )
    assert response[str(tc.id)]["status"] == "integracao_ok"
    assert response[str(tc.id)]["status_details"] == ""
    assert response[str(tc.id)]["vagas"] is None
    tc.max_split_value = D("149.90")
    tc.save()

    response = rodoviaria_svc.check_status_integracao_trechos(
        status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, False
    )
    assert response[str(tc.id)]["status"] == "integracao_price_error (R$139.9)"
    assert response[str(tc.id)]["status_details"] == ""
    assert response[str(tc.id)]["vagas"] is None
    assert response[str(tc.id)]["max_split_value"] == D("149.90")
    tc.refresh_from_db()
    assert tc.max_split_value == D("149.90")
    assert tc.vagas is None
    assert TrechoClasseMarketplaceLogger.objects.filter(trecho_classe_id=tc.id).exists() is False


def test_check_status_integracao_trechos_integracao_atualiza_vagas_nao_atualiza_preco(mocker):
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    tc = baker.make(
        "core.TrechoClasse",
        max_split_value=D("100.00"),
        grupo=grupo,
        vagas=10,
        price_manager=baker.make(PriceManager),
        datetime_ida=timezone.now(),
    )
    mocker.patch.object(
        RodoviariaClient,
        "get_company_info",
        return_value={
            "company_internal_id": grupo.company_id,
            "modelo_venda": grupo.modelo_venda,
            "features": ["nao_atualiza_preco"],
        },
    )
    status_rodoviaria = {str(tc.id): {"status": "integracao_ok", "preco_rodoviaria": 139.9, "vagas": 5}}
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    assert response[str(tc.id)]["status"] == "integracao_ok"
    assert response[str(tc.id)]["status_details"] == "(vagas atualizadas de: 10 para: 5)"
    assert response[str(tc.id)]["vagas"] == 5
    tc.refresh_from_db()
    assert tc.vagas == 5
    assert tc.max_split_value == D("100")


def test_check_status_integracao_trechos_integracao_nao_atualiza_vagas(mocker):
    origem, destino = baker.make("core.LocalEmbarque", _quantity=2)
    rota = baker.make("core.Rota", origem=origem, destino=destino)
    baker.make("core.Checkpoint", local=origem, rota=rota)
    baker.make("core.Checkpoint", local=destino, rota=rota)
    trecho_vendido = baker.make("core.TrechoVendido", origem=origem, destino=destino, rota=rota)
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE, rota=rota)
    tc = baker.make(
        "core.TrechoClasse",
        max_split_value=D("100.00"),
        grupo=grupo,
        vagas=10,
        price_manager=baker.make(PriceManager),
        datetime_ida=timezone.now(),
        grupo_classe__capacidade=15,
        trecho_vendido=trecho_vendido,
    )
    mocker.patch.object(
        RodoviariaClient,
        "get_company_info",
        return_value={
            "company_internal_id": grupo.company_id,
            "modelo_venda": grupo.modelo_venda,
            "features": ["nao_atualiza_vagas"],
        },
    )
    status_rodoviaria = {str(tc.id): {"status": "integracao_ok", "preco_rodoviaria": 139.9, "vagas": 5}}
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    assert response[str(tc.id)]["status"] == "integracao_ok"
    assert response[str(tc.id)]["status_details"] == (
        "(preço atualizado de: 100.00 para: 139.9)(vagas_api=5)(vagas recalculadas pelo capacity manager: de 10 para 15)"
    )
    assert response[str(tc.id)]["vagas"] == 15
    tc.refresh_from_db()
    assert tc.vagas == 15
    assert tc.max_split_value == D("139.9")


def test_check_status_integracao_trechos_integracao_nao_atualiza_vagas_nem_preco(mocker):
    origem, destino = baker.make("core.LocalEmbarque", _quantity=2)
    rota = baker.make("core.Rota", origem=origem, destino=destino)
    baker.make("core.Checkpoint", local=origem, rota=rota)
    baker.make("core.Checkpoint", local=destino, rota=rota)
    trecho_vendido = baker.make("core.TrechoVendido", origem=origem, destino=destino, rota=rota)
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE, rota=rota)
    tc = baker.make(
        "core.TrechoClasse",
        max_split_value=D("100.00"),
        grupo=grupo,
        vagas=10,
        price_manager=baker.make(PriceManager),
        datetime_ida=timezone.now(),
        grupo_classe__capacidade=10,
        trecho_vendido=trecho_vendido,
        closed=True,
        closed_reason="Sem vagas disponíveis na API",
    )
    mocker.patch.object(
        RodoviariaClient,
        "get_company_info",
        return_value={
            "company_internal_id": grupo.company_id,
            "modelo_venda": grupo.modelo_venda,
            "features": ["nao_atualiza_vagas", "nao_atualiza_preco"],
        },
    )
    status_rodoviaria = {str(tc.id): {"status": "integracao_ok", "preco_rodoviaria": 139.9, "vagas": 5}}
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    assert response[str(tc.id)]["status"] == "integracao_ok"
    assert response[str(tc.id)]["status_details"] == "(vagas_api=5)(trecho reaberto)"
    assert response[str(tc.id)]["vagas"] == 10
    tc.refresh_from_db()
    assert tc.vagas == 10
    assert tc.max_split_value == D("100")


def test_check_status_integracao_trechos_integracao_fecha_grupo_hibrido_nao_encontrado(mocker):
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.HIBRIDO)
    tc = baker.make(
        "core.TrechoClasse",
        max_split_value=D("100.00"),
        grupo=grupo,
        vagas=10,
        price_manager=baker.make(PriceManager),
        datetime_ida=timezone.now(),
    )
    mocker.patch.object(
        RodoviariaClient,
        "get_company_info",
        return_value={
            "company_internal_id": grupo.company_id,
            "modelo_venda": grupo.modelo_venda,
            "features": ["nao_atualiza_preco"],
        },
    )
    status_rodoviaria = {str(tc.id): {"status": "integracao_nao_encontrada"}}
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    assert response[str(tc.id)]["status"] == "integracao_nao_encontrada"
    assert response[str(tc.id)]["status_details"] == "(trecho fechado)"
    tc.refresh_from_db()
    assert tc.closed is True
    assert tc.closed_reason == "[NOT_FOUND] Serviço não encontrado na API"


def test_check_status_integracao_trechos_integracao_fecha_grupo_hibrido_sem_vagas(mocker):
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.HIBRIDO)
    tc = baker.make(
        "core.TrechoClasse",
        max_split_value=D("100.00"),
        grupo=grupo,
        vagas=10,
        price_manager=baker.make(PriceManager),
        datetime_ida=timezone.now(),
    )
    mocker.patch.object(
        RodoviariaClient,
        "get_company_info",
        return_value={
            "company_internal_id": grupo.company_id,
            "modelo_venda": grupo.modelo_venda,
            "features": ["nao_atualiza_preco"],
        },
    )
    status_rodoviaria = {str(tc.id): {"status": "integracao_ok", "preco_rodoviaria": 139.9, "vagas": 0}}
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    assert response[str(tc.id)]["status"] == "integracao_ok"
    assert response[str(tc.id)]["status_details"] == "(vagas atualizadas de: 10 para: 0)(trecho fechado)"
    tc.refresh_from_db()
    assert tc.closed is True
    assert tc.closed_reason == "[NO_SEATS_AVAILABLE] Sem vagas disponíveis na API"


def test_check_status_integracao_trechos_integracao_ok(mocker):
    mocker.patch.object(RodoviariaClient, "get_company_info", return_value={"empresas": {"features": []}})
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    tc = baker.make(
        "core.TrechoClasse", max_split_value=D("139.90"), datetime_ida=dateutils.now() + timedelta(days=1), grupo=grupo
    )
    status_rodoviaria = {str(tc.id): {"status": "integracao_ok", "preco_rodoviaria": 139.9, "vagas": 5}}
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    assert response[str(tc.id)]["status"] == "integracao_ok"
    assert response[str(tc.id)]["status_details"] == "(vagas atualizadas de: None para: 5)"
    assert response[str(tc.id)]["vagas"] == 5
    assert tc.vagas == 5

    log = TrechoClasseMarketplaceLogger.objects.filter(trecho_classe_id=tc.id).order_by("-created_at")[0]
    assert log.preco_old == D("139.90")
    assert log.preco_new == D("139.90")
    assert log.vagas_old is None
    assert log.vagas_new == 5

    # setup for second assert
    tc.max_split_value = D("149.90")
    tc.vagas = None
    tc.save()

    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    assert response[str(tc.id)]["status"] == "integracao_ok"
    assert (
        response[str(tc.id)]["status_details"]
        == "(preço atualizado de: 149.90 para: 139.9)(vagas atualizadas de: None para: 5)"
    )
    assert response[str(tc.id)]["vagas"] == 5
    assert response[str(tc.id)]["max_split_value"] == D("139.90")
    tc.refresh_from_db()
    assert tc.max_split_value == D("139.90")
    assert tc.vagas == 5
    log = TrechoClasseMarketplaceLogger.objects.filter(trecho_classe_id=tc.id).order_by("-created_at")[0]
    assert log.preco_old == D("149.90")
    assert log.preco_new == D("139.90")
    assert log.vagas_old is None
    assert log.vagas_new == 5


def test_check_status_integracao_trechos_um_trecho_sobrando_no_rodoviaria(mocker):
    mocker.patch.object(RodoviariaClient, "get_company_info", return_value={"empresas": {"features": []}})
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    tc = baker.make(
        "core.TrechoClasse", max_split_value=D("139.90"), datetime_ida=dateutils.now() + timedelta(days=1), grupo=grupo
    )
    tc_closed = baker.make(
        "core.TrechoClasse",
        max_split_value=D("139.90"),
        datetime_ida=dateutils.now() + timedelta(days=1),
        grupo=grupo,
        closed_reason=ClosedReasons.CLEANUP_OLD_CLASSES,
    )
    status_rodoviaria = {
        str(tc.id): {"status": "integracao_ok", "preco_rodoviaria": 139.9, "vagas": 5},
        str(tc_closed.id): {"status": "integracao_ok", "preco_rodoviaria": 139.9, "vagas": 5},
    }
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    assert response[str(tc.id)]["status"] == "integracao_ok"
    assert response[str(tc.id)]["status_details"] == "(vagas atualizadas de: None para: 5)"
    assert response[str(tc.id)]["vagas"] == 5
    assert tc.vagas == 5

    log = TrechoClasseMarketplaceLogger.objects.filter(trecho_classe_id=tc.id).order_by("-created_at")[0]
    assert log.preco_old == D("139.90")
    assert log.preco_new == D("139.90")
    assert log.vagas_old is None
    assert log.vagas_new == 5


def test_check_status_integracao_trechos_integracao_sem_vagas(mocker):
    mocker.patch.object(RodoviariaClient, "get_company_info", return_value={"empresas": {"features": []}})
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    tc = baker.make(
        "core.TrechoClasse", max_split_value=D("139.90"), datetime_ida=dateutils.now() + timedelta(days=1), grupo=grupo
    )
    status_rodoviaria = {str(tc.id): {"status": "integracao_ok", "preco_rodoviaria": 139.9, "vagas": 0}}
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    assert response[str(tc.id)]["status"] == "integracao_ok"
    assert response[str(tc.id)]["status_details"] == "(vagas atualizadas de: None para: 0)(trecho fechado)"
    assert response[str(tc.id)]["vagas"] == 0
    assert response[str(tc.id)]["closed"] is True
    tc.refresh_from_db()
    assert tc.max_split_value == D("139.90")
    assert tc.vagas == 0
    assert tc.closed
    assert tc.closed_reason == "[NO_SEATS_AVAILABLE] Sem vagas disponíveis na API"

    log = TrechoClasseMarketplaceLogger.objects.filter(trecho_classe_id=tc.id).order_by("-created_at")[0]
    assert log.preco_old == D("139.90")
    assert log.preco_new == D("139.90")
    assert log.vagas_old is None
    assert log.vagas_new == 0
    assert log.closed_old is False
    assert log.closed_new is True
    assert log.closed_reason_old is None
    assert log.closed_reason_new == "[NO_SEATS_AVAILABLE] Sem vagas disponíveis na API"


def test_check_status_integracao_trechos_varios_trechos(mocker):
    mocker.patch.object(RodoviariaClient, "get_company_info", return_value={"empresas": {"features": []}})
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    tc1 = baker.make(
        "core.TrechoClasse", max_split_value=D("139.90"), datetime_ida=dateutils.now() + timedelta(days=1), grupo=grupo
    )
    tc2 = baker.make(
        "core.TrechoClasse", max_split_value=D("139.90"), datetime_ida=dateutils.now() + timedelta(days=1), grupo=grupo
    )
    status_rodoviaria = {
        str(tc1.id): {"status": "integracao_ok", "preco_rodoviaria": 149.9, "vagas": 5},
        str(tc2.id): {
            "status": "integracao_nao_encontrada",
            "preco_rodoviaria": None,
            "vagas": None,
        },
    }
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(
        status_rodoviaria,
        {
            tc1.id: tc1,
            tc2.id: tc2,
        },
        gatilho_atualizacao,
        True,
    )

    assert response[str(tc1.id)]["status"] == "integracao_ok"
    assert (
        response[str(tc1.id)]["status_details"]
        == "(preço atualizado de: 139.90 para: 149.9)(vagas atualizadas de: None para: 5)"
    )
    assert response[str(tc1.id)]["vagas"] == 5
    assert response[str(tc1.id)]["max_split_value"] == D("149.90")

    assert response[str(tc2.id)]["status"] == "integracao_nao_encontrada"
    assert response[str(tc2.id)]["status_details"] == "(trecho fechado)"
    assert response[str(tc2.id)]["closed"] is True
    assert response[str(tc2.id)]["closed_reason"] == "[NOT_FOUND] Serviço não encontrado na API"

    tc1.refresh_from_db()
    tc2.refresh_from_db()
    assert tc1.max_split_value == D("149.90")
    assert tc1.vagas == 5
    assert tc2.closed
    assert tc2.closed_reason == "[NOT_FOUND] Serviço não encontrado na API"


def test_check_status_integracao_trechos_integracao_nao_encontrada(mocker):
    mocker.patch.object(RodoviariaClient, "get_company_info", return_value={"empresas": {"features": []}})
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    tc = baker.make(
        "core.TrechoClasse", max_split_value=D("139.90"), datetime_ida=dateutils.now() + timedelta(days=1), grupo=grupo
    )
    status_rodoviaria = {
        str(tc.id): {
            "status": "integracao_nao_encontrada",
            "preco_rodoviaria": None,
            "vagas": None,
            "error": "Serviço não encontrado na API: [['20:00', 'leito']]",
        }
    }
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    assert response[str(tc.id)]["status"] == "integracao_nao_encontrada"
    assert response[str(tc.id)]["status_details"] == "(trecho fechado)"
    assert response[str(tc.id)]["closed"] is True
    assert response[str(tc.id)]["closed_reason"] == "[NOT_FOUND] Serviço não encontrado na API: [['20:00', 'leito']]"
    tc.refresh_from_db()
    assert tc.max_split_value == D("139.90")
    assert tc.closed
    assert tc.closed_reason == "[NOT_FOUND] Serviço não encontrado na API: [['20:00', 'leito']]"


def test_check_status_integracao_trechos_api_nao_respondeu(mocker):
    mocker.patch.object(RodoviariaClient, "get_company_info", return_value={"empresas": {"features": []}})
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    tc = baker.make(
        "core.TrechoClasse", max_split_value=D("139.90"), datetime_ida=dateutils.now() + timedelta(days=1), grupo=grupo
    )
    status_rodoviaria = {
        str(tc.id): {
            "status": "erro_api_nao_respondeu",
            "preco_rodoviaria": None,
            "vagas": None,
        }
    }
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    tc.refresh_from_db()
    assert response[str(tc.id)]["status"] == "erro_api_nao_respondeu"
    assert response[str(tc.id)]["status_details"] == "(API não respondeu conforme esperado)"
    assert response[str(tc.id)]["closed"] is False
    assert tc.closed is False


def test_check_status_integracao_trechos_erro_inesperado(mocker):
    mocker.patch.object(RodoviariaClient, "get_company_info", return_value={"empresas": {"features": []}})
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    tc = baker.make(
        "core.TrechoClasse", max_split_value=D("139.90"), datetime_ida=dateutils.now() + timedelta(days=1), grupo=grupo
    )
    status_rodoviaria = {
        str(tc.id): {
            "status": "erro_inesperado",
            "preco_rodoviaria": None,
            "vagas": None,
            "error": "Aqui vai um erro muito grande da API que poluiria bastante a mensagem que aparece no staff",
        }
    }
    gatilho_atualizacao = TrechoClasseMarketplaceLogger.GatilhoAtualizacao.RODOVIARIA_LINK_MANUAL
    response = rodoviaria_svc.check_status_integracao_trechos(status_rodoviaria, {tc.id: tc}, gatilho_atualizacao, True)
    tc.refresh_from_db()
    assert response[str(tc.id)]["status"] == "erro_inesperado"
    assert response[str(tc.id)]["status_details"] == "(Aqui vai um erro muito grande ...)"
    assert response[str(tc.id)]["closed"] is False
    assert tc.closed is False


def _efetua_compra_mock(travels):
    passengers = [
        BuseiroForm(
            id=1,
            name="Zezinho",
            phone="*********",
            buyer_cpf=None,
            cpf=None,
            rg_number=None,
            rg_orgao=None,
            tipo_documento=None,
            birthday=date(1950, 10, 10),
            dados_beneficio=None,
        ),
    ]
    form = ComprarForm(
        travel_id=travels[0].id,
        trechoclasse_id=1,
        poltronas=[1],
        extra_poltronas="extra_poltronas",
        valor_cheio=travels[0].max_split_value,
        buseiros=passengers,
        categoria_especial=CategoriaEspecial.IDOSO_100,
    )
    response = {
        "passagens": [
            {
                "id": 1,
                "localizador": "81123",
                "bpe": "http://qrcode.com/123",
                "monitriip": "123321456654123321456654123321456654",
                "buseiro_id": 1,
                "travel_id": travels[0].id,
                "poltrona": 1,
            }
        ]
    }
    return form, response


def test_escala_motorista():
    driver = baker.make(
        "auth.User",
        first_name="Drivenildo",
        last_name="Silva",
        email="<EMAIL>",
        profile__cell_phone="11*********",
        profile__cpf="*********01",
    )
    grupo = baker.make(
        Grupo,
        modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        driver_one=driver,
        driver_two=None,
    )

    with (
        mock.patch("core.service.rodoviaria_svc._is_integrado") as mock_is_integrado,
        mock.patch("core.service.rodoviaria_svc._client._session.post") as mock_req,
    ):
        mock_is_integrado.return_value = True
        rodoviaria_svc.escala_motoristas(grupo)

        mock_req.assert_called_once_with(
            f"{rodoviaria_svc._client.api_url}/v1/motoristas/escala",
            json={
                "grupo_id": grupo.id,
                "motorista": {
                    "user_id": driver.id,
                    "nome": "Drivenildo Silva",
                    "email": "<EMAIL>",
                    "telefone": "11*********",
                    "cpf": "*********01",
                },
            },
            headers={
                "User-Agent": "buser_django/rodoviaria",
            },
        )


def is_jsonable(x):
    try:
        json.dumps(x)
        return True
    except (TypeError, OverflowError):
        return False


@pytest.fixture
def mock_get_itinerarios_marketplace():
    company = baker.make(Company)
    lc1 = baker.make(LocalEmbarque, nickname="São Paulo Tietê")
    lc2 = baker.make(LocalEmbarque, nickname="Taubaté")
    rota = baker.make(Rota)
    baker.make(
        Grupo,
        rota=rota,
        datetime_ida=timezone.now(),
        status="pending",
        confirming_probability="high",
        is_extra=False,
        modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    with mock.patch(
        "core.service.rodoviaria_svc._client.get_itinerarios_marketplace"
    ) as get_itinerarios_marketplace_mock:
        get_itinerarios_marketplace_mock.return_value = {
            "company_internal_id": company.id,
            "company_name": "Adamantina",
            "items": [
                {
                    "rodoviaria_rota_id": 6,
                    "id_internal": rota.id,
                    "checkpoints": [
                        {
                            "local_id": lc1.id,
                            "cidade_id": 234,
                            "local": {
                                "name": "BRASILIA - DF",
                                "uf": "DF",
                                "nickname": "BRASILIA - DF",
                            },
                        },
                        {
                            "local_id": lc2.id,
                            "cidade_id": 222,
                            "local": {
                                "name": "ANAPOLIS - GO",
                                "uf": "GO",
                                "nickname": "ANAPOLIS - GO",
                            },
                        },
                    ],
                },
                {
                    "rodoviaria_rota_id": 7,
                    "id_internal": None,
                    "checkpoints": [
                        {
                            "local_id": lc1.id,
                            "cidade_id": 234,
                            "local": {
                                "name": "BRASILIA - DF",
                                "uf": "DF",
                                "nickname": "BRASILIA - DF",
                            },
                        },
                        {
                            "local_id": None,
                            "cidade_id": 222,
                            "local": {
                                "name": "ANAPOLIS - GO",
                                "uf": "GO",
                                "nickname": "ANAPOLIS - GO",
                            },
                        },
                    ],
                },
            ],
            "count": 8,
            "num_pages": 4,
        }
        yield SimpleNamespace(lc1=lc1, lc2=lc2, company=company)
    get_itinerarios_marketplace_mock.assert_called_once()


def test_get_itinerarios_marketplace(mock_get_itinerarios_marketplace):
    company = mock_get_itinerarios_marketplace.company
    lc1 = mock_get_itinerarios_marketplace.lc1
    baker.make(
        LocalRetiradaMarketplace,
        tipo="guiche",
        descricao="Empresa",
        company=company,
        local_embarque=lc1,
    )
    response = rodoviaria_svc.get_itinerarios_marketplace(93231, {})

    assert response["items"][0]["count_grupos"] == 1
    assert "count_grupos" not in response["items"][1]

    assert response["items"][0]["checkpoints"][0]["local_id"] == lc1.id
    assert response["items"][0]["checkpoints"][0]["local"]["id"] == lc1.id
    assert response["items"][0]["checkpoints"][0]["local"]["nickname"] == lc1.nickname
    assert response["items"][0]["checkpoints"][0]["local_retirada"]["tipo"] == "guiche"
    assert response["items"][0]["checkpoints"][0]["local_retirada"]["descricao"] == "Empresa"
    assert response["items"][0]["locais_retirada_cadastrados"] is False

    # assert response["items"][0]["trechos_vendidos"][0]["origem_id"] == lc1.id
    # assert response["items"][0]["trechos_vendidos"][0]["origem"]["id"] == lc1.id
    # assert (
    #     response["items"][0]["trechos_vendidos"][0]["origem"]["nickname"]
    #     == lc1.nickname
    # )

    # assert response["items"][0]["trechos_vendidos"][0]["destino_id"] == lc2.id
    # assert response["items"][0]["trechos_vendidos"][0]["destino"]["id"] == lc2.id
    # assert (
    #     response["items"][0]["trechos_vendidos"][0]["destino"]["nickname"]
    #     == lc2.nickname
    # )

    assert is_jsonable(response)


def test_get_itinerarios_marketplace_todos_locais_retirada_cadastrados(
    mock_get_itinerarios_marketplace,
):
    company = mock_get_itinerarios_marketplace.company
    lc1 = mock_get_itinerarios_marketplace.lc1
    baker.make(
        LocalRetiradaMarketplace,
        tipo="guiche",
        descricao="Empresa",
        company=company,
        local_embarque=lc1,
    )
    lc2 = mock_get_itinerarios_marketplace.lc2
    baker.make(
        LocalRetiradaMarketplace,
        tipo="motorista",
        descricao="Plataforma 13",
        company=company,
        local_embarque=lc2,
    )
    response = rodoviaria_svc.get_itinerarios_marketplace(company.id, {})

    assert response["items"][0]["count_grupos"] == 1
    assert "count_grupos" not in response["items"][1]

    assert response["items"][0]["checkpoints"][0]["local_id"] == lc1.id
    assert response["items"][0]["checkpoints"][0]["local"]["id"] == lc1.id
    assert response["items"][0]["checkpoints"][0]["local"]["nickname"] == lc1.nickname
    assert response["items"][0]["checkpoints"][0]["local_retirada"]["tipo"] == "guiche"
    assert response["items"][0]["checkpoints"][0]["local_retirada"]["descricao"] == "Empresa"
    assert response["items"][0]["locais_retirada_cadastrados"] is True


def test_get_itinerarios_marketplace_nenhum_local_linkado():
    with mock.patch(
        "core.service.rodoviaria_svc._client.get_itinerarios_marketplace"
    ) as get_itinerarios_marketplace_mock:
        get_itinerarios_marketplace_mock.return_value = {
            "company_internal_id": 123,
            "company_name": "Adamantina",
            "items": [
                {
                    "rodoviaria_rota_id": 6,
                    "id_internal": 7832,
                    "checkpoints": [
                        {
                            "local_id": None,
                            "cidade_id": 234,
                            "local": {
                                "name": "BRASILIA - DF",
                                "uf": "DF",
                                "nickname": "BRASILIA - DF",
                            },
                        }
                    ],
                }
            ],
            "count": 1,
            "num_pages": 4,
        }
        response = rodoviaria_svc.get_itinerarios_marketplace(123, {})
    assert response["items"][0]["locais_retirada_cadastrados"] is False


def test_get_itinerarios_marketplace_sem_rotas():
    with mock.patch(
        "core.service.rodoviaria_svc._client.get_itinerarios_marketplace"
    ) as get_itinerarios_marketplace_mock:
        get_itinerarios_marketplace_mock.return_value = {
            "company_internal_id": 326,
            "company_name": "Mingoti",
            "items": [],
            "count": 0,
            "num_pages": -1,
        }
        response = rodoviaria_svc.get_itinerarios_marketplace(326, {})

    get_itinerarios_marketplace_mock.assert_called_once()
    assert response["count"] == 0

    assert is_jsonable(response)


def test_cadastrar_trecho_rodoviaria():
    company = baker.make("core.Company")
    grupo = baker.make("core.Grupo", company=company)
    grupo_classe = baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="semi leito")
    origem = baker.make("core.LocalEmbarque", cidade=baker.make("core.Cidade"))
    destino = baker.make("core.LocalEmbarque", cidade=baker.make("core.Cidade"))
    trecho_vendido = baker.make("core.TrechoVendido", origem=origem, destino=destino)
    trecho_classe = baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        grupo_classe=grupo_classe,
        trecho_vendido=trecho_vendido,
        max_split_value=D("120"),
    )
    with mock.patch("core.service.rodoviaria_svc._client.cadastrar_trechos_rodoviaria") as cadastra_trecho_mock:
        cadastra_trecho_mock.return_value = {"message": "Cadastro de trechos iniciado"}
        response = rodoviaria_svc.cadastrar_trecho_rodoviaria(trecho_classe_id=trecho_classe.id)
    assert response == cadastra_trecho_mock.return_value
    cadastra_trecho_mock.assert_called_once_with(
        {
            "company_id": company.id,
            "trechos": [
                {
                    "cidade_origem_id": origem.cidade_id,
                    "cidade_destino_id": destino.cidade_id,
                    "local_origem_id": origem.id,
                    "local_destino_id": destino.id,
                    "classe": grupo_classe.tipo_assento,
                    "max_split_value": trecho_classe.max_split_value,
                }
            ],
        }
    )


def test_tempo_total_checkpoint():
    assert rodoviaria_svc.tempo_total_checkpoint(SimpleNamespace(duracao=None, tempo_embarque=1), 1, 4) == 1
    assert rodoviaria_svc.tempo_total_checkpoint(SimpleNamespace(duracao=1, tempo_embarque=1), 1, 4) == 2
    assert rodoviaria_svc.tempo_total_checkpoint(SimpleNamespace(duracao=0, tempo_embarque=1), 1, 4) == 1
    assert rodoviaria_svc.tempo_total_checkpoint(SimpleNamespace(duracao=1, tempo_embarque=1), 3, 4) == 1


@pytest.fixture
def dados_crud_rota_hibrido():
    company = baker.make("core.Company")
    rota = baker.make("core.Rota")
    cidade = baker.make("core.Cidade")
    origem = baker.make("core.LocalEmbarque", cidade=cidade, nickname="Gruta")
    autorizacao_hibrido = baker.make(
        "core.AutorizacaoHibrido",
        prefixo="82131-932",
        cidade_origem=baker.make("core.Cidade"),
        cidade_destino=baker.make("core.Cidade"),
    )
    grupo = baker.make("core.Grupo", company=company, rota=rota, autorizacao_hibrido=autorizacao_hibrido)
    checkpoint = baker.make(
        "core.Checkpoint",
        local=origem,
        rota=rota,
        distancia_km=100,
        duracao="01:30",
        tempo_embarque="0:20",
    )
    return company, rota, grupo, cidade, origem, checkpoint


def test_criar_itinerario_hibrido(dados_crud_rota_hibrido):
    company, rota, grupo, cidade, origem, checkpoint = dados_crud_rota_hibrido
    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.criar_itinerario_hibrido",
            return_value={"message": "Itinerário criado"},
        ) as criar_itinerario_hibrido_mock,
        mock.patch(
            "core.service.rodoviaria_svc._client.cadastrar_trechos_rodoviaria",
            return_value={"message": "Trechos cadastrados"},
        ) as cadastrar_trechos_mock,
    ):
        response = rodoviaria_svc.criar_itinerario_hibrido(
            RotaHibridoForm.parse_obj({"company_id": company.id, "grupo_id": grupo.id, "rota_id": 1})
        )
    assert response == criar_itinerario_hibrido_mock.return_value
    criar_itinerario_hibrido_mock.assert_called_once_with(
        {
            "company_id": company.id,
            "id_rota_internal": rota.id,
            "id_rota_external": 1,
            "checkpoints": [
                {
                    "local_embarque_id": origem.id,
                    "cidade_destino_id": cidade.id,
                    "duracao": "1:30",
                    "tempo_embarque": "0:20",
                    "tempo_total": "1:30",
                    "ponto_embarque": "Gruta",
                    "km": 100,
                }
            ],
        }
    )
    cadastrar_trechos_mock.assert_called_once_with({"company_id": company.id, "trechos": []})


def test_atualizar_embarques_hibrido(dados_crud_rota_hibrido):
    company, rota, grupo, cidade, origem, checkpoint = dados_crud_rota_hibrido

    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.atualizar_embarques_hibrido",
            return_value={"message": "Locais de embarque atualizados"},
        ) as atualizar_embarques_hibrido_mock,
        mock.patch(
            "core.service.rodoviaria_svc._client.cadastrar_trechos_rodoviaria",
            return_value={"message": "Trechos cadastrados"},
        ) as cadastrar_trechos_mock,
    ):
        response = rodoviaria_svc.atualizar_embarques_hibrido(
            RotaHibridoForm.parse_obj({"company_id": company.id, "grupo_id": grupo.id, "rota_id": 1})
        )
    assert response == atualizar_embarques_hibrido_mock.return_value
    atualizar_embarques_hibrido_mock.assert_called_once_with(
        {
            "company_id": company.id,
            "id_rota_internal": rota.id,
            "id_rota_external": 1,
            "checkpoints": [
                {
                    "local_embarque_id": origem.id,
                    "cidade_destino_id": cidade.id,
                    "duracao": "1:30",
                    "tempo_embarque": "0:20",
                    "tempo_total": "1:30",
                    "ponto_embarque": "Gruta",
                    "km": 100,
                }
            ],
        }
    )
    cadastrar_trechos_mock.assert_called_once_with({"company_id": company.id, "trechos": []})


def test_editar_ou_criar_rota_hibrido(dados_crud_rota_hibrido):
    company, rota, grupo, cidade, origem, checkpoint = dados_crud_rota_hibrido
    cidades = baker.make(Cidade, _quantity=2)
    autorizacao = baker.make(
        AutorizacaoHibrido,
        cidade_origem=cidades[0],
        cidade_destino=cidades[1],
        prefixo="123-456",
    )

    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.editar_ou_criar_rota_hibrido"
        ) as editar_ou_criar_rota_hibrido_mock,
        mock.patch("core.service.rodoviaria_svc.cadastrar_trechos_rodoviaria_por_grupo") as cadastrar_trechos_mock,
    ):
        response = rodoviaria_svc.editar_ou_criar_rota_hibrido(grupo, autorizacao, [4, 3, 25], rota_rodoviaria_id=4925)
    assert response == editar_ou_criar_rota_hibrido_mock.return_value
    editar_ou_criar_rota_hibrido_mock.assert_called_once_with(
        {
            "company_id": company.id,
            "id_rota_internal": rota.id,
            "id_rota_external": 4925,
            "grupo_ids": [4, 3, 25],
            "cidade_origem_id": cidades[0].id,
            "cidade_destino_id": cidades[1].id,
            "prefixo": "123-456",
            "checkpoints": [
                {
                    "local_embarque_id": origem.id,
                    "cidade_destino_id": cidade.id,
                    "duracao": "1:30",
                    "tempo_embarque": "0:20",
                    "tempo_total": "1:30",
                    "ponto_embarque": "Gruta",
                    "km": 100,
                }
            ],
        }
    )
    cadastrar_trechos_mock.assert_called_once_with(grupo.id)


def test_sincronizar_rota_hibrido_por_grupo(dados_crud_rota_hibrido):
    company, rota, grupo, cidade, origem, checkpoint = dados_crud_rota_hibrido
    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.sincronizar_rota",
            return_value={"message": "Rota Sincronizada"},
        ) as sincronizar_rota_mock,
        mock.patch(
            "core.service.rodoviaria_svc.cadastrar_trechos_rodoviaria_por_grupo",
        ) as cadastrar_trechos_rodoviaria_por_grupo,
    ):
        rodoviaria_svc.sincronizar_rota_hibrido_por_grupo(grupo_id=grupo.id)
    cadastrar_trechos_rodoviaria_por_grupo.assert_called_once_with(grupo.id)
    sincronizar_rota_mock.assert_called_once_with(
        {
            "company_id": company.id,
            "id_rota_internal": rota.id,
            "cidade_destino_id": grupo.autorizacao_hibrido.cidade_destino_id,
            "cidade_origem_id": grupo.autorizacao_hibrido.cidade_origem_id,
            "prefixo": grupo.autorizacao_hibrido.prefixo,
            "checkpoints": [
                {
                    "local_embarque_id": origem.id,
                    "cidade_destino_id": cidade.id,
                    "duracao": "1:30",
                    "tempo_embarque": "0:20",
                    "tempo_total": "1:30",
                    "ponto_embarque": "Gruta",
                    "km": 100,
                }
            ],
        }
    )


def test_sincronizar_rota_hibrido_por_grupo_autorizacao_nao_cadastrada(dados_crud_rota_hibrido):
    company, rota, grupo, cidade, origem, checkpoint = dados_crud_rota_hibrido
    grupo.autorizacao_hibrido = None
    grupo.save()
    with pytest.raises(AutorizacaoNaoCadastradaRodoviaria):
        rodoviaria_svc.sincronizar_rota_hibrido_por_grupo(grupo_id=grupo.id)


def test_criar_rota_hibrido(dados_crud_rota_hibrido):
    company, rota, grupo, cidade, origem, checkpoint = dados_crud_rota_hibrido
    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.criar_rota_hibrido",
            return_value={"message": "Itinerário criado"},
        ) as criar_rota_hibrido_mock,
        mock.patch(
            "core.service.rodoviaria_svc._client.cadastrar_trechos_rodoviaria",
            return_value={"message": "Trechos cadastrados"},
        ) as cadastrar_trechos_mock,
    ):
        response = rodoviaria_svc.criar_rota_hibrido(
            CriarRotaHibridoForm.parse_obj(
                {
                    "company_id": company.id,
                    "grupo_id": grupo.id,
                    "cidade_origem_id": 1,
                    "cidade_destino_id": 2,
                    "prefixo": "19-25",
                }
            )
        )
    assert response == criar_rota_hibrido_mock.return_value
    criar_rota_hibrido_mock.assert_called_once_with(
        {
            "company_id": company.id,
            "id_rota_internal": rota.id,
            "cidade_destino_id": 2,
            "cidade_origem_id": 1,
            "prefixo": "19-25",
            "checkpoints": [
                {
                    "local_embarque_id": origem.id,
                    "cidade_destino_id": cidade.id,
                    "duracao": "1:30",
                    "tempo_embarque": "0:20",
                    "tempo_total": "1:30",
                    "ponto_embarque": "Gruta",
                    "km": 100,
                }
            ],
        }
    )
    cadastrar_trechos_mock.assert_called_once_with({"company_id": company.id, "trechos": []})


def test_editar_rota_hibrido(dados_crud_rota_hibrido):
    company, rota, grupo, cidade, origem, checkpoint = dados_crud_rota_hibrido
    baker.make(
        "core.Grupo",
        rota=rota,
        company=company,
        datetime_ida=timezone.now() + timedelta(hours=1),
    )
    grupo_3_horas = baker.make(
        "core.Grupo",
        rota=rota,
        company=company,
        datetime_ida=timezone.now() + timedelta(hours=4),
    )
    # validar que o solicita_cancelamento_travels foi chamado com o id só da de 3 horas
    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.editar_rota_hibrido",
            return_value={"message": "Rota atualizada"},
        ) as editar_rota_hibrido_mock,
        mock.patch(
            "core.service.rodoviaria_svc._client.cadastrar_trechos_rodoviaria",
            return_value={"message": "Trechos cadastrados"},
        ) as cadastrar_trechos_mock,
    ):
        response = rodoviaria_svc.criar_rota_hibrido(
            CriarRotaHibridoForm.parse_obj(
                {
                    "company_id": company.id,
                    "grupo_id": grupo.id,
                    "cidade_origem_id": 1,
                    "cidade_destino_id": 2,
                    "prefixo": "19-25",
                    "rota_rodoviaria_id": 888,
                    "grupo_ids": [grupo_3_horas.id],
                }
            )
        )
    assert response == editar_rota_hibrido_mock.return_value
    editar_rota_hibrido_mock.assert_called_once_with(
        {
            "company_id": company.id,
            "id_rota_internal": rota.id,
            "cidade_destino_id": 2,
            "cidade_origem_id": 1,
            "prefixo": "19-25",
            "id_rota_external": 888,
            "grupo_ids": [grupo_3_horas.id],
        }
    )
    cadastrar_trechos_mock.assert_called_once_with({"company_id": company.id, "trechos": []})


def test_cadastrar_trechos_rodoviaria_por_grupo():
    company = baker.make("core.Company")
    grupo = baker.make("core.Grupo", company=company)
    grupo_classe_1 = baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="semi leito")
    origem = baker.make("core.LocalEmbarque", cidade=baker.make("core.Cidade"))
    destino_1 = baker.make("core.LocalEmbarque", cidade=baker.make("core.Cidade"))
    trecho_vendido_1 = baker.make("core.TrechoVendido", origem=origem, destino=destino_1)
    trecho_classe_1 = baker.make(
        "core.TrechoClasse",
        grupo=grupo,
        grupo_classe=grupo_classe_1,
        trecho_vendido=trecho_vendido_1,
        max_split_value=D("120"),
    )
    with mock.patch("core.service.rodoviaria_svc._client.cadastrar_trechos_rodoviaria") as cadastra_preco_mock:
        cadastra_preco_mock.return_value = {"message": "Cadastro de trechos iniciado"}
        response = rodoviaria_svc.cadastrar_trechos_rodoviaria_por_grupo(grupo_id=grupo.id)
    assert response == cadastra_preco_mock.return_value
    cadastra_preco_mock.assert_called_once_with(
        {
            "company_id": company.id,
            "trechos": [
                {
                    "cidade_origem_id": origem.cidade_id,
                    "cidade_destino_id": destino_1.cidade_id,
                    "local_origem_id": origem.id,
                    "local_destino_id": destino_1.id,
                    "classe": grupo_classe_1.tipo_assento,
                    "max_split_value": trecho_classe_1.max_split_value,
                },
            ],
        }
    )


def test_ids_empresas_marketplace_auto_integra_operacao():
    with mock.patch("core.service.rodoviaria_svc._client.get_empresas_by_features") as mock_empresas_integradas:
        mock_empresas_integradas.return_value = {
            "empresas": [
                {"company_internal_id": 49, "modelo_venda": "hibrido", "features": ["auto_integra_operacao"]},
                {"company_internal_id": 21, "modelo_venda": "marketplace", "features": ["auto_integra_operacao"]},
            ]
        }
        response = rodoviaria_svc.ids_empresas_marketplace_auto_integra_operacao()
    assert response == [21]


def test_list_empresas_hibridas_ids():
    with mock.patch("core.service.rodoviaria_svc._client.get_empresas_integradas") as mock_empresas_integradas:
        mock_empresas_integradas.return_value = {
            "empresas": [
                {"company_internal_id": 49, "modelo_venda": "marketplace"},
                {"company_internal_id": 21, "modelo_venda": "hibrido"},
            ]
        }
        response = rodoviaria_svc.list_empresas_hibridas_ids()
    assert response == [21]


def test_list_empresas_marketplace_ids():
    with mock.patch("core.service.rodoviaria_svc._client.get_empresas_integradas") as mock_empresas_integradas:
        mock_empresas_integradas.return_value = {
            "empresas": [
                {"company_internal_id": 49, "modelo_venda": "marketplace"},
                {"company_internal_id": 21, "modelo_venda": "hibrido"},
            ]
        }
        response = rodoviaria_svc.list_empresas_marketplace_ids()
    assert response == [49]


def test_cadastrar_grupos_hibridos_rodoviaria_params(requests_mock):
    json_response = {
        "rotas": [
            {"descricao": "Rio -> São Paulo", "id": 1},
            {"descricao": "São Paulo -> Rio", "id": 2},
        ],
        "last_option": 2,
    }
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/hibrido/cadastrar-grupos-params",
        json=json_response,
    )
    response = rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria_params(123, 87382)
    assert response == json_response


def test_cadastrar_grupos_hibridos_rodoviaria():
    company_id = 123
    rota = baker.make(
        "core.Rota",
        origem=baker.make(
            "core.LocalEmbarque",
            cidade=baker.make("core.Cidade", timezone="America/Campo_Grande"),
        ),
    )
    onibus = baker.make("core.Onibus", placa="ABC1234")
    oc_semi_leito = baker.make("core.OnibusClasse", onibus=onibus, tipo="semi leito", capacidade=30)
    oc_leito = baker.make("core.OnibusClasse", onibus=onibus, tipo="leito", capacidade=13)
    grupo = baker.make(
        "core.Grupo",
        datetime_ida=to_default_tz_required(datetime(2022, 1, 5, 18, 30)),
        rota=rota,
        onibus=onibus,
    )
    gc_semi_leito = baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="semi leito")
    gc_leito = baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="leito")
    params = CadastrarGruposHibridosForm.parse_obj(
        {"company_id": company_id, "grupos_ids": f"{[grupo.id]}", "rota_id": 48}
    )
    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.cadastrar_grupos_hibridos_rodoviaria"
        ) as mock_cadastrar_grupos_hibridos_rodoviaria,
        mock.patch(
            "core.service.rodoviaria_svc.sincronizar_rotas_hibrido_por_grupos"
        ) as mock_sincronizar_rotas_hibrido_por_grupos,
    ):
        rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria(params.company_id, params.grupos_ids)
    expected_json = {
        "company_id": company_id,
        "grupos": [
            {
                "data_partida": "2022-01-05",
                "hora_saida": "17:30",
                "veiculo_placa": "ABC1234",
                "rota_internal_id": rota.id,
                "veiculo_internal_id": onibus.id,
                "grupo_id": grupo.id,
                "classes": [
                    {
                        "grupo_classe_id": gc_leito.id,
                        "tipo": oc_leito.tipo,
                        "capacidade": oc_leito.capacidade,
                    },
                    {
                        "grupo_classe_id": gc_semi_leito.id,
                        "tipo": oc_semi_leito.tipo,
                        "capacidade": oc_semi_leito.capacidade,
                    },
                ],
            }
        ],
    }

    mock_cadastrar_grupos_hibridos_rodoviaria.assert_called_once_with(expected_json)
    mock_sincronizar_rotas_hibrido_por_grupos.assert_called_once_with([grupo])


def test_cadastrar_grupos_hibridos_rodoviaria_rota_nao_encontrada():
    company_id = 123
    rota = baker.make(
        "core.Rota",
        origem=baker.make(
            "core.LocalEmbarque",
            cidade=baker.make("core.Cidade", timezone="America/Campo_Grande"),
        ),
    )
    onibus = baker.make("core.Onibus", placa="ABC1234")
    baker.make("core.OnibusClasse", onibus=onibus, tipo="semi leito", capacidade=30)
    baker.make("core.OnibusClasse", onibus=onibus, tipo="leito", capacidade=13)
    grupo = baker.make(
        "core.Grupo",
        datetime_ida=to_default_tz_required(datetime(2022, 1, 5, 18, 30)),
        rota=rota,
        onibus=onibus,
    )
    baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="semi leito")
    baker.make("core.GrupoClasse", grupo=grupo, tipo_assento="leito")
    params = CadastrarGruposHibridosForm.parse_obj(
        {"company_id": company_id, "grupos_ids": f"{[grupo.id]}", "rota_id": 48}
    )
    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.cadastrar_grupos_hibridos_rodoviaria"
        ) as mock_cadastrar_grupos_hibridos_rodoviaria,
        pytest.raises(
            RotaNaoCadastradaRodoviaria,
            match=f"rota {rota.id} nao cadastrada no rodoviaria para empresa {company_id}",
        ),
        mock.patch(
            "core.service.rodoviaria_svc.sincronizar_rotas_hibrido_por_grupos"
        ) as mock_sincronizar_rotas_hibrido_por_grupos,
    ):
        mock_cadastrar_grupos_hibridos_rodoviaria.return_value = {
            "error": "erro",
            "error_type": "rota_nao_cadastrada",
            "rota_id": rota.id,
            "company_id": company_id,
        }
        rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria(params.company_id, params.grupos_ids)
    mock_sincronizar_rotas_hibrido_por_grupos.assert_called_once_with([grupo])


def test_cancelar_grupo_marketplace():
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    travels = [baker.make("core.Travel", grupo=grupo)]
    # com travels
    with (
        mock.patch("core.service.rodoviaria_svc.solicitar_cancelamento_travels_grupo") as mock_solicitar_cancelamentos,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as mock_is_integrado,
    ):
        mock_is_integrado.return_value = True
        rodoviaria_svc.cancelar_grupo(grupo, travels)
    mock_solicitar_cancelamentos.assert_called_once_with(grupo, travels)
    # sem travels
    with (
        mock.patch("core.service.rodoviaria_svc.solicitar_cancelamento_travels_grupo") as mock_solicitar_cancelamentos,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as mock_is_integrado,
    ):
        mock_is_integrado.return_value = True
        rodoviaria_svc.cancelar_grupo(grupo)
    mock_solicitar_cancelamentos.assert_called_once_with(grupo, travels)


def test_cancelar_grupo_integrado():
    company = baker.make("core.Company")
    grupo = baker.make("core.Grupo", company=company)
    travel = baker.make("core.Travel", grupo=grupo)
    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.solicitar_cancelamento_travels",
            return_value={"message": "Grupos Classe cancelados"},
        ) as mock_solicitar_cancelamento_travels,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as mock_is_integrado,
    ):
        mock_is_integrado.return_value = True
        rodoviaria_svc.cancelar_grupo(grupo)
    mock_solicitar_cancelamento_travels.assert_called_once_with([travel.id])


def test_update_grupos_classe_rodoviaria():
    grupo_integrado = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.HIBRIDO)
    grupo_nao_integrado = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.BUSER)
    grupo_classe_1 = baker.make("core.GrupoClasse", grupo=grupo_integrado)
    grupo_classe_1.OLD_ID = 111
    grupo_classe_2 = baker.make("core.GrupoClasse", grupo=grupo_nao_integrado)
    grupo_classe_2.OLD_ID = 222
    grupo_classe_3 = baker.make("core.GrupoClasse", grupo=grupo_integrado)
    grupo_classe_3.OLD_ID = 333
    with mock.patch(
        "core.service.rodoviaria_svc._client.update_grupos_classe_rodoviaria"
    ) as mock_update_grupos_classe_rodoviaria:
        mock_update_grupos_classe_rodoviaria.return_value = {"message": "Link de Grupos Classe atualizados"}
        response = rodoviaria_svc.update_grupos_classe_rodoviaria([grupo_classe_1, grupo_classe_2, grupo_classe_3])
    assert response is None
    mock_update_grupos_classe_rodoviaria.assert_called_once_with(
        [
            {"new_id": grupo_classe_1.id, "old_id": 111},
            {"new_id": grupo_classe_3.id, "old_id": 333},
        ]
    )


def test_recadastrar_grupos_hibridos(time_machine):
    time_machine.move_to(to_default_tz_required("2022-08-12 14:04:54"))
    company = baker.make("core.Company")
    rota = baker.make("core.Rota")
    onibus = baker.make("core.Onibus")
    grupo = baker.make(
        "core.Grupo",
        rota=rota,
        onibus=onibus,
        datetime_ida=timezone.now() + timedelta(hours=5),
        company=company,
        modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        status=Grupo.Status.TRAVEL_CONFIRMED,
    )
    with mock.patch(
        "core.service.rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria"
    ) as mock_cadastrar_grupos_hibridos_rodoviaria:
        rodoviaria_svc._recadastrar_grupos_hibridos(rota.id)
    mock_cadastrar_grupos_hibridos_rodoviaria.delay.assert_called_once_with(company.id, [grupo.id])


def test_update_grupos_classe_rodoviaria_nao_chama_rodoviaria():
    grupo_nao_integrado = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.BUSER)
    grupo_classe = baker.make("core.GrupoClasse", grupo=grupo_nao_integrado)
    grupo_classe.OLD_ID = 444
    with mock.patch(
        "core.service.rodoviaria_svc._client.update_grupos_classe_rodoviaria"
    ) as mock_update_grupos_classe_rodoviaria:
        mock_update_grupos_classe_rodoviaria.return_value = {"message": "Link de Grupos Classe atualizados"}
        response = rodoviaria_svc.update_grupos_classe_rodoviaria([grupo_classe])
    assert response is None
    mock_update_grupos_classe_rodoviaria.assert_not_called()


@mock.patch("core.service.rodoviaria_svc._client.update_grupos_classe_rodoviaria")
def test_update_grupos_classe_rodoviaria_nao_causa_n_mais_um_queries(
    mock_update_grupos_classe_rodoviaria, django_assert_num_queries
):
    """Garante que não haverá n+1 por falta de select_related entre grupo_classe e grupo"""
    grupos_hibridos = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.HIBRIDO, _quantity=3)
    old_ids = [100, 101, 102, 103, 104, 105]  # 3 grupos x 2 classes
    gc_ids = []
    for g in grupos_hibridos:
        for tipo_assento in ["leito", "executivo"]:  # 2 grupo_classe por grupo
            grupo_classe = baker.make("core.GrupoClasse", tipo_assento=tipo_assento, grupo=g)
            gc_ids.append(grupo_classe.id)

    # Não pode montar a lista de grupo_classe no loop acima, pois a relação entre grupo e grupo_classe já está cacheada
    # fazer uma nova query garante que não tem nada em cache.
    grupos_classe = GrupoClasse.objects.filter(pk__in=gc_ids)
    for old_id, gc in zip(old_ids, grupos_classe):
        setattr(gc, "OLD_ID", old_id)

    with django_assert_num_queries(1):
        # A query é do prefetch_related_objects.
        # O global_setting _is_prod() está mockado aqui, então não faz query.
        rodoviaria_svc.update_grupos_classe_rodoviaria(grupos_classe)


def test_escalar_onibus_grupo_hibrido_com_passageiros():
    company = baker.make("core.Company")
    cidade_origem = baker.make("core.Cidade", timezone="America/Sao_Paulo")
    origem = baker.make("core.LocalEmbarque", cidade=cidade_origem)
    rota = baker.make("core.Rota", origem=origem)
    grupo = baker.make(
        "core.Grupo",
        company=company,
        rota=rota,
        datetime_ida=to_default_tz_required(datetime(2022, 2, 2, 15, 30)),
    )
    onibus = baker.make("core.Onibus", placa="ABC1234")
    baker.make("core.OnibusClasse", onibus=onibus, tipo="leito", capacidade=30)
    grupo_classe = baker.make("core.GrupoClasse", tipo_assento="leito", grupo=grupo)
    grupos_classe_ids_antigos = [5940, 5941]
    with mock.patch("core.service.rodoviaria_svc.alterar_grupo_hibrido.s") as mock_alterar_onibus_grupo_hibrido:
        rodoviaria_svc.escalar_onibus_grupo_hibrido(grupo, onibus, grupos_classe_ids_antigos)
    mock_alterar_onibus_grupo_hibrido.assert_called_once_with(
        grupo.id,
        {
            "company_id": company.id,
            "grupos_classe_ids_antigos": [5940, 5941],
            "grupo": {
                "data_partida": "2022-02-02",
                "hora_saida": "15:30",
                "veiculo_placa": "ABC1234",
                "rota_internal_id": rota.id,
                "veiculo_internal_id": onibus.id,
                "grupo_id": grupo.id,
                "classes": [
                    {
                        "grupo_classe_id": grupo_classe.id,
                        "tipo": "leito",
                        "capacidade": 30,
                    }
                ],
            },
        },
    )


def test_alterar_horario_grupo_hibrido_com_passageiros():
    company = baker.make("core.Company")
    cidade_origem = baker.make("core.Cidade", timezone="America/Sao_Paulo")
    origem = baker.make("core.LocalEmbarque", cidade=cidade_origem)
    rota = baker.make("core.Rota", origem=origem)
    onibus = baker.make("core.Onibus", placa="ABC1234")
    grupo = baker.make(
        "core.Grupo",
        company=company,
        rota=rota,
        datetime_ida=to_default_tz_required(datetime(2022, 2, 2, 15, 30)),
        onibus=onibus,
    )
    baker.make("core.OnibusClasse", onibus=onibus, tipo="leito", capacidade=30)
    grupo_classe = baker.make("core.GrupoClasse", tipo_assento="leito", grupo=grupo)
    with mock.patch("core.service.rodoviaria_svc.alterar_grupo_hibrido.s") as mock_alterar_onibus_grupo_hibrido:
        rodoviaria_svc.alterar_horario_grupo_hibrido(grupo)
    mock_alterar_onibus_grupo_hibrido.assert_called_once_with(
        grupo.id,
        {
            "company_id": company.id,
            "grupos_classe_ids_antigos": [grupo_classe.id],
            "grupo": {
                "data_partida": "2022-02-02",
                "hora_saida": "15:30",
                "veiculo_placa": "ABC1234",
                "rota_internal_id": rota.id,
                "veiculo_internal_id": onibus.id,
                "grupo_id": grupo.id,
                "classes": [
                    {
                        "grupo_classe_id": grupo_classe.id,
                        "tipo": "leito",
                        "capacidade": 30,
                    }
                ],
            },
        },
    )


def test_alterar_onibus_grupo_hibrido():
    grupo = baker.make("core.Grupo")
    origem, destino = baker.make("core.LocalEmbarque", _quantity=2)
    trecho_vendido = baker.make("core.TrechoVendido", origem=origem, destino=destino)
    trecho_classe = baker.make("core.TrechoClasse", grupo=grupo, trecho_vendido=trecho_vendido)
    user = baker.make("auth.User", profile=baker.make("core.Profile", cell_phone="12921211212"))
    baker.make("core.Profile", user=user, cell_phone="12921211212")
    buseiro = baker.make("core.Buseiro", name="Nome", cpf="22390940013", rg_number="*********", user=user)
    travel = baker.make(
        "core.Travel",
        max_split_value=D("110"),
        grupo=grupo,
        trecho_classe=trecho_classe,
    )
    baker.make("core.Passageiro", travel=travel, buseiro=buseiro)
    with mock.patch("core.service.rodoviaria_svc._client.alterar_grupo_hibrido") as mock_alterar_onibus_grupo_hibrido:
        rodoviaria_svc.alterar_grupo_hibrido(grupo.id, {})
    mock_alterar_onibus_grupo_hibrido.assert_called_once_with(
        {
            "passageiros": [
                {
                    "trechoclasse_id": trecho_classe.id,
                    "valor_por_buseiro": None,
                    "id_origem": origem.id,
                    "id_destino": destino.id,
                    "travels": [
                        {
                            "travel_id": travel.id,
                            "valor_por_buseiro": 110.0,
                            "buseiros": [
                                {
                                    "id": buseiro.id,
                                    "name": "Nome",
                                    "cpf": "22390940013",
                                    "rg_number": "*********",
                                    "phone": "12921211212",
                                }
                            ],
                        }
                    ],
                }
            ]
        }
    )


def test_create_empresa_marketplace():
    company = baker.make("core.Company")
    with mock.patch("core.service.rodoviaria_svc._client.create_company", return_value={}) as mock_create_company:
        rodoviaria_svc.create_empresa(
            company.name, company.id, "marketplace", ["feature1", "feature2"], None, 45, "totalbus"
        )
    mock_create_company.assert_called_once_with(
        name=company.name,
        company_internal_id=company.id,
        modelo_venda="marketplace",
        features=["feature1", "feature2"],
        login=None,
        max_percentual_divergencia=45,
        integracao="totalbus",
    )


def test_create_empresa_hibrido():
    company_transbrasil = baker.make("core.Company", id=1003)
    company = baker.make("core.Company")
    with mock.patch(
        "core.service.rodoviaria_svc._client.create_company_transbrasil",
        return_value={},
    ) as mock_create_company_transbrasil:
        rodoviaria_svc.create_empresa(
            company.name, company.id, "hibrido", None, {"company_external_id": 999}, None, "totalbus"
        )
    mock_create_company_transbrasil.assert_called_once_with(
        name=company.razao_social,
        company_internal_id=company.id,
        company_external_id=999,
    )
    assert globalsettings_svc.get("show_rodoviaria_staff_features") == [company.id]
    company.refresh_from_db()
    assert company.parent_company_hibrido == company_transbrasil


def test_sincronizar_rotas_hibrido_por_grupos():
    rota_1 = baker.make(Rota)
    company_1 = baker.make(Company)
    grupo_1 = baker.make(Grupo, rota=rota_1, company=company_1)
    rota_2 = baker.make(Rota)
    company_2 = baker.make(Company)
    grupo_2 = baker.make(Grupo, rota=rota_2, company=company_2)
    with mock.patch(
        "core.service.rodoviaria_svc.sincronizar_rota_hibrido_por_grupo"
    ) as mock_sincronizar_rota_hibrido_por_grupo:
        rodoviaria_svc.sincronizar_rotas_hibrido_por_grupos([grupo_1, grupo_2])
    assert mock_sincronizar_rota_hibrido_por_grupo.call_count == 2
    assert mock_sincronizar_rota_hibrido_por_grupo.call_args_list[0][0][0] == grupo_1
    assert mock_sincronizar_rota_hibrido_por_grupo.call_args_list[1][0][0] == grupo_2


def test_handle_rodoviaria_after_error():
    ex = RodoviariaViagemBloqueada(trechoclasse_id=12)
    with mock.patch("core.service.rodoviaria_svc._fecha_trecho"):
        rodoviaria_svc.handle_rodoviaria_after_error(ex)


def test_update_empresa():
    name = "empresa ldta"
    company_internal_id = 18
    modelo_venda = "marketplace"
    features = ["add_pax_staff", "active"]
    login = {}
    max_percentual_divergencia = 100
    integracao = "totalbus"

    with mock.patch("core.service.rodoviaria_svc._client.update_company") as mock_client:
        rodoviaria_svc.update_empresa(
            name, company_internal_id, modelo_venda, features, login, max_percentual_divergencia, integracao
        )

    mock_client.assert_called_once_with(
        name=name,
        company_internal_id=company_internal_id,
        modelo_venda=modelo_venda,
        features=features,
        login=login,
        max_percentual_divergencia=max_percentual_divergencia,
        integracao=integracao,
    )


def test_get_empresa_login():
    integracao = "totalbus"
    company_id = 18
    modelo_venda = "marketplace"

    with mock.patch("core.service.rodoviaria_svc._client.get_company_login") as mock_client:
        rodoviaria_svc.get_empresa_login(integracao, company_id, modelo_venda)

    mock_client.assert_called_once_with(integracao=integracao, company_id=company_id, modelo_venda=modelo_venda)


def test_fetch_locais_empresa():
    company_rodoviaria_id = 18

    with mock.patch("core.service.rodoviaria_svc._client.fetch_company_locais") as mock_client:
        rodoviaria_svc.fetch_locais_empresa(company_rodoviaria_id)

    mock_client.assert_called_once_with(company_rodoviaria_id=company_rodoviaria_id)


def test_get_locais_retirada_empresa(marketplace_company):
    company_internal_id = 188966
    local_pk = 78965
    marketplace_company.pk = company_internal_id
    items = [
        {
            "id": 14563,
            "empresa": 17,
            "cidade_external": "MACEIO - AL #82",
            "localidade_external": "Maceio - Al #82",
            "local_embarque_buser": local_pk,
            "buser_cidade_id": None,
        },
        {
            "id": 14565,
            "empresa": 17,
            "cidade_external": "APARECIDA (SP) -  #597",
            "localidade_external": "Aparecida (Sp) -  #597",
            "local_embarque_buser": None,
            "buser_cidade_id": None,
        },
    ]

    locais_rodov = {"items": items, "count": len(items), "num_pages": 1}
    local_embarque = baker.make(
        LocalEmbarque,
        pk=local_pk,
        nickname="Praca central",
        description="Embarque na praca central da cidade",
        endereco="Avenida Dr. Buser 550",
    )
    local_retirada = baker.make(
        LocalRetiradaMarketplace,
        company_id=company_internal_id,
        local_embarque_id=local_pk,
        tipo=str(LocalRetiradaMarketplace.LocalRetirada.GUICHE),
        descricao="viacao estrela",
    )
    company_rodoviaria_id = 23123
    with mock.patch(
        "core.service.rodoviaria_svc._client.list_links_local_embarque",
        return_value=locais_rodov,
    ) as mock_list_links_local_embarque:
        resp = rodoviaria_svc.get_locais_retirada_empresa(
            company_internal_id, company_rodoviaria_id, associado_rota=None, linkado_buser=None
        )
    mock_list_links_local_embarque.assert_called_once_with(
        {"empresa_id_filter": company_rodoviaria_id, "associado_rota": None, "linkado_buser": None}
    )

    assert {
        "local_embarque_API": locais_rodov["items"][0]["localidade_external"],
        "local_embarque_nome": local_embarque.nickname,
        "local_embarque_endereco": local_embarque.endereco,
        "local_embarque_descricao": local_embarque.description,
        "local_retirada_descricao": f"{local_retirada.tipo} - {local_retirada.descricao}",
    } in resp
    assert {
        "local_embarque_API": locais_rodov["items"][1]["localidade_external"],
        "local_embarque_nome": "",
        "local_embarque_endereco": "",
        "local_embarque_descricao": "",
        "local_retirada_descricao": "",
    } in resp


def test_fetch_external_totalbus_companies():
    user = "user_anon"
    password = "le_senhe"
    tenant_id = "9e48bc2b-2707-4b79-85fd-6cf34253d8c5"

    with mock.patch(
        "core.service.rodoviaria_svc._client.fetch_external_totalbus_companies",
    ) as mock_client:
        rodoviaria_svc.fetch_external_totalbus_companies(user, password, tenant_id)

    mock_client.assert_called_once_with(user=user, password=password, tenant_id=tenant_id)


def test_fetch_formas_pagamento():
    params = ListaFormasPagamentoRodoviariaForm.parse_obj(
        {
            "integracao": "totalbus",
            "login": {"user": "user_anon", "password": "le_senhe", "tenantId": "9e48bc2b-2707-4b79-85fd-6cf34253d8c5"},
        }
    )

    with mock.patch(
        "core.service.rodoviaria_svc._client.fetch_formas_pagamento",
    ) as mock_client:
        rodoviaria_svc.fetch_formas_pagamento(params)

    assert isinstance(params.login, TotalbusLoginForm)
    mock_client.assert_called_once_with(params.integracao, params.login.dict())


def test_reservar_erro_pagamento_desbloqueia_poltronas(client, mocker):
    user = baker.make("auth.User", profile=baker.make("core.Profile", cell_phone="12921211212", cpf="45699350879"))
    client.force_login(user)
    datetime_ida = to_default_tz_required(datetime(2022, 6, 14, 8, 40))
    tc = baker.make(
        TrechoClasse, datetime_ida=datetime_ida, grupo=baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    )
    mocker.patch(
        "core.service.reserva.reserva_svc.reserva_eventos_svc.new_event_cria_reserva",
        return_value=SimpleNamespace(
            grupos=[SimpleNamespace(id=tc.id, grupo_id=2819)],
            data={"extrato": "extrato", "payment": {"payment": "pago"}, "passengers": {"name": "jose"}},
            passengers=[{"name": "jose", "id": 456}, {"name": "joao", "id": 457}],
            extrato="extrato",
            trecho_classe_ids=[tc.id],
            user_should_pay=True,
            categoria_especial="",
            trechos_ida=[],
            trechos_volta=[],
            travels=[
                {"trechoclasse_id": tc.id},
            ],
            trechos=[tc],
            user=None,
            poltronas_bloqueadas=None,
        ),
    )
    mocker.patch("core.service.buseiro_svc.upsert_buseiros", return_value=["buseiros"])
    poltronas_bloqueadas = [
        SimpleNamespace(trecho_classe_id=tc.id, numero_poltrona=49),
        SimpleNamespace(trecho_classe_id=tc.id, numero_poltrona=50),
    ]
    mocker.patch(
        "core.service.reserva.reserva_svc.bloqueia_poltronas",
        return_value=poltronas_bloqueadas,
    )
    mock_desbloqueia_poltronas = mocker.patch("core.service.selecao_assento.marketplace.desbloquear_poltrona.delay")
    mock_executa_pagamento = mocker.patch("core.service.reserva.reserva_svc._executa_pagamento")
    mock_executa_pagamento.side_effect = Exception("erro")
    with pytest.raises(Exception):
        fixtures.reserva(user=user, trecho_classe=tc, name="Usuario Teste")

    calls = [
        mocker.call(trecho_classe=poltrona.trecho_classe_id, poltrona=poltrona.numero_poltrona)
        for poltrona in poltronas_bloqueadas
    ]
    mock_desbloqueia_poltronas.assert_has_calls(calls, any_order=True)


def test_atualizar_link_trechos_classes():
    datetime_ida = datetime(2022, 6, 14, 8, 40, tzinfo=timezone.get_default_timezone())
    mock_grupo = fixtures.grupos_e_trechos(dtbase=datetime_ida, status="travel_confirmed")
    with mock.patch(
        "core.service.rodoviaria_svc._client.atualizar_link_trechos_classes",
    ) as mock_client:
        rodoviaria_svc.atualizar_link_trechos_classes(
            [
                mock_grupo.trecho_classe_bhsp,
            ],
            False,
            False,
            False,
        )

    args_to_assert = []
    args_to_assert.append(
        {
            "trechoclasse_internal_id": mock_grupo.trecho_classe_bhsp.id,
            "origem_internal_id": mock_grupo.trecho_classe_bhsp.trecho_vendido.origem_id,
            "destino_internal_id": mock_grupo.trecho_classe_bhsp.trecho_vendido.destino_id,
            "datetime_ida": (
                to_tz_required(
                    datetime_ida,
                    mock_grupo.trecho_classe_bhsp.trecho_vendido.origem.cidade.timezone,
                ).strftime("%Y-%m-%dT%H:%M:%S")
            ),
            "grupo_datetime_ida": (
                to_tz_required(
                    mock_grupo.trecho_classe_bhsp.grupo.datetime_ida,
                    mock_grupo.trecho_classe_bhsp.trecho_vendido.origem.cidade.timezone,
                ).strftime("%Y-%m-%dT%H:%M:%S")
            ),
            "company_internal_id": mock_grupo.trecho_classe_bhsp.grupo.company_id,
            "modelo_venda": mock_grupo.trecho_classe_bhsp.grupo.modelo_venda,
            "grupo_internal_id": mock_grupo.trecho_classe_bhsp.grupo.id,
            "grupoclasse_internal_id": mock_grupo.grupo_classe_bhsp.id,
            "tipo_assento": mock_grupo.grupo_classe_bhsp.tipo_assento,
            "origem_timezone": mock_grupo.trecho_classe_bhsp.trecho_vendido.origem.cidade.timezone,
            "extra": mock_grupo.trecho_classe_bhsp.extra.get("mkp_extra"),
        }
    )
    mock_client.assert_called_with(args_to_assert, False, False, False)


def test_verifica_criacao_grupos_hibrido():
    company = baker.make("core.Company")
    grupo_hibrido = baker.make(
        Grupo,
        modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        onibus=baker.make("core.Onibus"),
        company=company,
    )
    with mock.patch(
        "core.service.rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria.delay"
    ) as mock_cadastrar_grupos_hibridos_rodoviaria:
        rodoviaria_svc.verifica_criacao_grupos_hibrido([grupo_hibrido])
    mock_cadastrar_grupos_hibridos_rodoviaria.assert_called_with(company.id, [grupo_hibrido.id])


def test_verifica_criacao_grupos_hibrido_company_desatualizada():
    company = baker.make("core.Company")
    company_atualizada = baker.make("core.Company")
    grupo_hibrido = baker.make(
        Grupo,
        modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        onibus=baker.make("core.Onibus"),
        company=company,
    )

    grupo_hibrido_from_db = Grupo.objects.get(id=grupo_hibrido.id)
    grupo_hibrido_from_db.company = company_atualizada
    grupo_hibrido_from_db.save()

    with mock.patch(
        "core.service.rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria.delay"
    ) as mock_cadastrar_grupos_hibridos_rodoviaria:
        rodoviaria_svc.verifica_criacao_grupos_hibrido([grupo_hibrido])
    mock_cadastrar_grupos_hibridos_rodoviaria.assert_called_with(company_atualizada.id, [grupo_hibrido.id])


def test_verifica_criacao_grupos_hibrido_marketplace():
    grupo_hibrido = baker.make(
        Grupo,
        modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        onibus=baker.make("core.Onibus"),
        company=baker.make("core.Company"),
    )
    with mock.patch(
        "core.service.rodoviaria_svc.cadastrar_grupos_hibridos_rodoviaria.delay"
    ) as mock_cadastrar_grupos_hibridos_rodoviaria:
        rodoviaria_svc.verifica_criacao_grupos_hibrido([grupo_hibrido])
    mock_cadastrar_grupos_hibridos_rodoviaria.assert_not_called()


def test_verifica_criacao_grupos_hibrido_n_empresas():
    company = baker.make("core.Company")
    grupos_hibridos = baker.make(
        Grupo,
        modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        onibus=baker.make("core.Onibus"),
        company=company,
        _quantity=2,
    )
    with mock.patch(
        "core.service.rodoviaria_svc.verifica_criacao_grupos_hibrido"
    ) as mock_verifica_criacao_grupos_hibrido:
        rodoviaria_svc.verifica_criacao_grupos_hibrido_n_empresas(grupos_hibridos)
    mock_verifica_criacao_grupos_hibrido.assert_called_with(grupos_hibridos)


def test_verifica_criacao_grupos_hibrido_n_rotas():
    company = baker.make("core.Company")
    grupos_hibridos = baker.make(
        Grupo,
        modelo_venda=Grupo.ModeloVenda.HIBRIDO,
        onibus=baker.make("core.Onibus"),
        rota=baker.make("core.Rota"),
        company=company,
        _quantity=2,
    )
    with mock.patch(
        "core.service.rodoviaria_svc.verifica_criacao_grupos_hibrido"
    ) as mock_verifica_criacao_grupos_hibrido:
        rodoviaria_svc.verifica_criacao_grupos_hibrido_n_rotas(grupos_hibridos)
    mock_verifica_criacao_grupos_hibrido.assert_called_with(grupos_hibridos)


def test_abrir_trechos():
    grupo = baker.make("core.Grupo")
    trecho_fechado = baker.make("core.TrechoClasse", closed=True, grupo=grupo)
    rodoviaria_svc.abrir_trechos([grupo.id])
    trecho_fechado.refresh_from_db()
    assert trecho_fechado.closed is False


def test_abrir_trechos_nao_abre_hard_stop():
    grupo = baker.make("core.Grupo")
    trecho_fechado = baker.make(
        "core.TrechoClasse",
        closed=True,
        grupo=grupo,
        closed_reason=f"[{rodoviaria_svc.HARD_STOP_PREFIX}] trecho bloqueado empresa X",
    )
    rodoviaria_svc.abrir_trechos([grupo.id])
    trecho_fechado.refresh_from_db()
    assert trecho_fechado.closed is True


def test_abrir_trechos_nao_abre_teste_hot_update():
    grupo = baker.make("core.Grupo")
    trecho_fechado = baker.make(
        "core.TrechoClasse",
        closed=True,
        grupo=grupo,
        closed_reason=DUPLICADO_NOVO_ESTOQUE_CLOSED_REASON,
    )
    rodoviaria_svc.abrir_trechos([grupo.id])
    trecho_fechado.refresh_from_db()
    assert trecho_fechado.closed is True


def test_fetch_trechos_vendidos_por_rota():
    rodov_rota_id = 12
    queue = "bp_trechos_vendidos"

    client_resp = {
        "task_id": "34558cd9-95f7-44da-b853-d4a9b457536e",
        "name": "rodoviaria.service.fetch_trechos_vendidos_svc.fetch_trecho_vendido_task",
        "queue": queue,
        "is_group_task": True,
        "status": "NOT_STARTED",
        "date_done": None,
        "mensagem": f"Fetch de trechos vendidos da rota {rodov_rota_id}",
    }

    with mock.patch(
        "core.service.rodoviaria_svc._client.fetch_trechos_vendidos_por_rota",
        return_value=client_resp,
    ):
        response = rodoviaria_svc.fetch_trechos_vendidos_por_rota(rodov_rota_id)

    assert response == client_resp


def test_atualizar_link_rota():
    with mock.patch("core.service.rodoviaria_svc._client.atualizar_link_rota") as mock_atualizar_link_rota:
        mock_atualizar_link_rota.return_value = {"message": "Foram atualizadas 1 rotas"}
        rodoviaria_svc._atualizar_link_rota(2, 777)
    mock_atualizar_link_rota.assert_called_once_with(2, 777)


def test_pos_edit_rota():
    rota = baker.make("core.Rota")
    trechos_vendidos = [SimpleNamespace(pk=10, OLD_ID=1), SimpleNamespace(pk=20, OLD_ID=2)]
    trechos_vendidos_map = {}
    for tv in trechos_vendidos:
        trechos_vendidos_map[tv.OLD_ID] = tv.pk
    with (
        mock.patch(
            "core.service.rodoviaria_svc._recadastrar_grupos_hibridos.delay"
        ) as mock_recadastrar_grupos_hibridos,
        mock.patch("core.service.rodoviaria_svc._atualizar_link_rota.delay") as mock_atualizar_link_rota,
        mock.patch(
            "core.service.rodoviaria_svc._atualizar_trechos_vendidos_de_rota.delay"
        ) as mock_atualizar_trechos_vendidos_de_rota,
    ):
        mock_recadastrar_grupos_hibridos.return_value = {"message": "Locais de embarque atualizados"}
        mock_atualizar_link_rota.return_value = {"message": "Foram atualizadas 1 rotas"}
        rodoviaria_svc.pos_edit_rota(rota.id, 777, trechos_vendidos)
    mock_recadastrar_grupos_hibridos.assert_called_once_with(rota.id)
    mock_atualizar_link_rota.assert_called_once_with(rota.id, 777)
    mock_atualizar_trechos_vendidos_de_rota.assert_called_once_with(rota.id, trechos_vendidos_map)


def test_pos_edit_rota_nao_raise_exception():
    rota = baker.make("core.Rota")
    trechos_vendidos = [SimpleNamespace(pk=10, OLD_ID=1), SimpleNamespace(pk=20, OLD_ID=2)]
    with (
        mock.patch(
            "core.service.rodoviaria_svc._recadastrar_grupos_hibridos.delay"
        ) as mock_recadastrar_grupos_hibridos,
        mock.patch("core.service.rodoviaria_svc._atualizar_link_rota.delay") as mock_atualizar_link_rota,
        mock.patch("core.service.rodoviaria_svc._atualizar_trechos_vendidos_de_rota.delay"),
    ):
        mock_recadastrar_grupos_hibridos.side_effect = Exception("excecao qualquer")
        mock_atualizar_link_rota.return_value = {"message": "Foram atualizadas 1 rotas"}
        rodoviaria_svc.pos_edit_rota(rota.id, 777, trechos_vendidos)
    mock_recadastrar_grupos_hibridos.assert_called_once_with(rota.id)
    mock_atualizar_link_rota.assert_called_once_with(rota.id, 777)


def test_verify_praxio_login():
    nome = "empresa_fantasma ltda"
    senha = "le_senhe"
    cliente = "empresa_vr"
    client_resp = {
        "id_sessao_op": "9EC144F1C974A9D141004DC76ACE83A98kFZldUbRvOPE",
        "id_estabelecimento": 601,
        "new_login": True,
    }

    with mock.patch(
        "core.service.rodoviaria_svc._client.verify_praxio_login",
        return_value=client_resp,
    ) as client_mock:
        rodoviaria_svc.verify_praxio_login(nome=nome, senha=senha, cliente=cliente)

    client_mock.assert_called_once_with(nome, senha, cliente)


def test_lista_empresas_api_totalbus():
    params = ListaEmpresasAPIParams.parse_obj(
        {
            "login_params": {
                "user": "usuario",
                "password": "senha",
                "tenantId": "tenant-id",
            }
        }
    )
    client_resp = [
        {"name": "Empresa 1", "id": 1},
        {"name": "Empresa 2", "id": 2},
    ]

    with mock.patch(
        "core.service.rodoviaria_svc._client.lista_empresas_api",
        return_value=client_resp,
    ) as client_mock:
        rodoviaria_svc.lista_empresas_api(params)

    client_mock.assert_called_once_with(params.login_params.dict())


def test_lista_empresas_api_vexado():
    params = ListaEmpresasAPIParams.parse_obj({"login_params": {"modelo_venda": "marketplace"}})
    client_resp = [
        {"name": "Empresa 1", "id": 1},
        {"name": "Empresa 2", "id": 2},
    ]

    with mock.patch(
        "core.service.rodoviaria_svc._client.lista_empresas_api",
        return_value=client_resp,
    ) as client_mock:
        rodoviaria_svc.lista_empresas_api(params)

    client_mock.assert_called_once_with(params.login_params.dict())


def test_passageiros_rodoviaria_por_company_periodo():
    companies_ids = [232]
    data_inicial = datetime(2022, 2, 10)
    data_final = datetime(2022, 2, 15)
    with mock.patch(
        "core.service.rodoviaria_svc._client.passageiros_rodoviaria_por_company_periodo",
        return_value=[{"travel_id": 123, "buseiro_id": 321, "passagem_id": 9}],
    ) as mock_passageiros:
        passageiros = rodoviaria_svc.passageiros_rodoviaria_por_company_periodo(
            companies_integradas_ids=companies_ids,
            data_inicial=data_inicial,
            data_final=data_final,
        )
    assert passageiros == {(123, 321): 9}
    mock_passageiros.assert_called_once_with(companies_ids, data_inicial, data_final)


def test_solicitar_cancelamento_passagens_por_id():
    passagens_ids = [231, 423]
    with mock.patch(
        "core.service.rodoviaria_svc._client.solicitar_cancelamento_por_ids"
    ) as mock_solicitar_cancelamento_por_ids:
        response = rodoviaria_svc.solicitar_cancelamento_passagens_por_id(passagens_ids)
    mock_solicitar_cancelamento_por_ids.assert_called_once_with(passagens_ids)
    assert response == mock_solicitar_cancelamento_por_ids.return_value


def test_get_trechos_overbooking(requests_mock):
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/get-trechos-overbooking",
        json={"trechos": [31, 8423]},
        status_code=200,
    )
    response = rodoviaria_svc.get_trechos_overbooking()
    assert response == {"trechos": [31, 8423]}


def test_get_trechos_sem_servico_hibrido(requests_mock):
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/hibrido/get-trechos-classe-error",
        json={"trechos": [31, 8423]},
        status_code=200,
    )
    response = rodoviaria_svc.get_trechos_sem_servico_hibrido()
    assert response == {"trechos": [31, 8423]}


def test_deleta_trechos_sem_servico_hibrido(requests_mock):
    requests_mock.post(
        f"{settings.RODOVIARIA_API_URL}/v1/hibrido/delete-trechos-classe-error",
        json={},
        status_code=200,
    )
    response = rodoviaria_svc.deleta_trechos_classe_error([31234, 423123])
    assert response == {}


def test_get_status_bpe(requests_mock):
    requests_mock.get(
        f"{settings.RODOVIARIA_API_URL}/v1/get-status-bpe",
        json={"em_contingencia": True},
        status_code=200,
    )
    response = rodoviaria_svc.get_status_bpe(233123)
    assert response == {"em_contingencia": True}


def test_tenta_atualizar_trechos_do_grupo_para_itinerario():
    grupo = baker.make("core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE, datetime_ida=timezone.now())
    tc1 = baker.make("core.TrechoClasse", max_split_value=D("139.90"), grupo=grupo, datetime_ida=timezone.now())
    tc2 = baker.make(
        "core.TrechoClasse",
        max_split_value=D("139.90"),
        grupo=grupo,
        datetime_ida=timezone.now() + timedelta(minutes=10),
    )
    tc3 = baker.make(
        "core.TrechoClasse",
        max_split_value=D("139.90"),
        grupo=grupo,
        datetime_ida=timezone.now() + timedelta(minutes=40),
    )
    with mock.patch(
        "core.service.rodoviaria_svc.update_status_integracao_trecho_classe"
    ) as mock_update_status_integracao_trecho_classe:
        mock_update_status_integracao_trecho_classe.return_value = {
            str(tc1.id): {"status": ""},
            str(tc2.id): {"status": ""},
            str(tc3.id): {"status": ""},
        }
        conseguiu = rodoviaria_svc.tenta_atualizar_trechos_do_grupo_para_itinerario(grupo.id, 3)
    assert mock_update_status_integracao_trecho_classe.call_count == 3
    assert not conseguiu

    with mock.patch(
        "core.service.rodoviaria_svc.update_status_integracao_trecho_classe"
    ) as mock_update_status_integracao_trecho_classe:
        mock_update_status_integracao_trecho_classe.return_value = {
            str(tc1.id): {"status": "integracao_ok"},
            str(tc2.id): {"status": ""},
            str(tc3.id): {"status": ""},
        }
        conseguiu = rodoviaria_svc.tenta_atualizar_trechos_do_grupo_para_itinerario(grupo.id, 3)
    assert mock_update_status_integracao_trecho_classe.call_count == 1
    assert conseguiu


def test_criar_grupos_markeplace(trechovendido_mock, trechoclasse_mock, user_staff, django_assert_num_queries):
    tc = trechovendido_mock
    rota = tc.rota
    rotinas = []
    for x in range(3):
        rotinas.append(
            CriarGruposMarketplaceForm.Rotina(
                horario="20:00",
                trechos_vendidos={tc.id: {"executivo": D("12.35"), "leito": D("1231.00")}},
                datas_filtradas=[timezone.now(), timezone.now()],
                classes=[
                    ClasseForm(
                        id="as", tipo_assento="executivo", max_capacity=6000, max_split_value=None, ref_split_value=None
                    ),
                    ClasseForm(
                        id="as2", tipo_assento="leito", max_capacity=200 + x, max_split_value=None, ref_split_value=None
                    ),
                ],
            )
        )
    company = baker.make(Company, cnpj="76852325000164")
    params = CriarGruposMarketplaceForm(
        rota_id=rota.id, company_id=company.id, percentual_repasse=85, percentual_taxa_servico=10, rotinas=rotinas
    )
    with django_assert_num_queries(77):
        grupos = rodoviaria_svc.criar_grupos_markeplace(params, user_staff)
    assert len(grupos) == 6
    for grupo in grupos:
        assert grupo.status == Grupo.Status.TRAVEL_CONFIRMED
        assert grupo.modelo_venda == Grupo.ModeloVenda.MARKETPLACE
        assert grupo.rota_id == rota.id
        assert grupo.percentual_repasse == 85
        assert grupo.company_id == company.id
        assert to_default_tz_required(grupo.datetime_ida).hour == 20
        assert len(grupo.trechoclasse_set.all()) == 2
        for trecho in grupo.trechoclasse_set.all():
            assert trecho.ref_split_value == trecho.max_split_value
            assert trecho.trecho_vendido_id == tc.id
            if trecho.grupo_classe.tipo_assento == "executivo":
                assert trecho.max_split_value == D("12.35")
            else:
                assert trecho.max_split_value == D("1231.00")


def test_criar_grupos_markeplace_sem_datas(trechovendido_mock, trechoclasse_mock, user_staff):
    tc = trechovendido_mock
    rota = tc.rota
    company = baker.make(Company, cnpj="76852325000164")
    rotinas = [
        CriarGruposMarketplaceForm.Rotina(
            horario="20:00",
            trechos_vendidos={tc.id: {"executivo": D("12.35"), "leito": D("1231.00")}},
            datas_filtradas=[],
            classes=[
                ClasseForm(
                    id="as", tipo_assento="executivo", max_capacity=6000, max_split_value=None, ref_split_value=None
                ),
                ClasseForm(
                    id="as2", tipo_assento="leito", max_capacity=200, max_split_value=None, ref_split_value=None
                ),
            ],
        )
    ]
    params = CriarGruposMarketplaceForm(
        rota_id=rota.id, company_id=company.id, percentual_repasse=85, percentual_taxa_servico=10, rotinas=rotinas
    )
    grupos = rodoviaria_svc.criar_grupos_markeplace(params, user_staff)
    assert len(grupos) == 0


def test_tem_grupos_abertos_pending():
    c = baker.make("core.Company")
    datetime_onibus = today_midnight() + timedelta(hours=12)
    r = baker.make("core.Rota")
    baker.make("core.Grupo", rota=r, company=c, datetime_ida=datetime_onibus, status=Grupo.Status.PENDING)
    tem_grupos_abertos = rodoviaria_svc.tem_grupos_abertos(r.id, c.id, [datetime_onibus])
    assert tem_grupos_abertos


def test_tem_grupos_abertos_travel_confirmed():
    c = baker.make("core.Company")
    datetime_onibus = today_midnight() + timedelta(hours=12)
    r = baker.make("core.Rota")
    baker.make("core.Grupo", rota=r, company=c, datetime_ida=datetime_onibus, status=Grupo.Status.TRAVEL_CONFIRMED)
    tem_grupos_abertos = rodoviaria_svc.tem_grupos_abertos(r.id, c.id, [datetime_onibus])
    assert tem_grupos_abertos


def test_tem_grupos_abertos_sem_grupos_abertos():
    datetime_onibus = today_midnight() + timedelta(hours=12)
    c = baker.make("core.Company")
    r = baker.make("core.Rota")
    # da rota, mas done. nao pega
    baker.make("core.Grupo", rota=r, company=c, datetime_ida=datetime_onibus, status=Grupo.Status.DONE)
    # da rota, mas canceled. nao pega
    baker.make("core.Grupo", rota=r, company=c, datetime_ida=datetime_onibus, status=Grupo.Status.CANCELED)
    # nao é da rota. nao pega
    baker.make("core.Grupo", datetime_ida=datetime_onibus, company=c, status=Grupo.Status.PENDING)
    # da rota, mas fora da lista de dias
    baker.make(
        "core.Grupo", rota=r, company=c, datetime_ida=datetime_onibus + timedelta(days=1), status=Grupo.Status.PENDING
    )
    baker.make(
        "core.Grupo", rota=r, company=c, datetime_ida=datetime_onibus - timedelta(days=1), status=Grupo.Status.PENDING
    )
    tem_grupos_abertos = rodoviaria_svc.tem_grupos_abertos(r.id, c.id, [datetime_onibus])
    assert not tem_grupos_abertos


def test_get_classes_e_precos_rota():
    rota = baker.make("core.Rota")
    t_ids = [baker.make("core.TrechoVendido", rota=rota).id, baker.make("core.TrechoVendido", rota=rota).id]
    with mock.patch("core.service.rodoviaria_svc._client.get_classes_e_precos_rota") as mock_client:
        rodoviaria_svc.get_classes_e_precos_rota(rota.id)
    assert mock_client.call_args[0][0] == rota.id
    assert mock_client.call_args[0][1] == t_ids


def test_remover_trechos_vendidos_de_rota():
    rota_id = 18
    trechos_vendidos_map = [{i: i * 10} for i in range(0, 3)]
    with mock.patch(
        "core.service.rodoviaria_svc._client.atualizar_trechos_vendidos_de_rota"
    ) as mock_remover_trechos_vendidos_de_rota:
        rodoviaria_svc._atualizar_trechos_vendidos_de_rota(rota_id, trechos_vendidos_map)

    mock_remover_trechos_vendidos_de_rota.assert_called_once_with(rota_id, trechos_vendidos_map)


def test_get_passagens_rodoviaria():
    kwargs = {
        "travel_ids": [1, 2, 3],
        "buseiro_ids": [4, 5, 6],
        "status_passagens": None,
        "with_preco_rodoviaria": False,
    }

    with mock.patch("core.service.rodoviaria_svc._client.get_passagens_rodoviaria") as mock_rodoviaria_client:
        rodoviaria_svc.get_passagens_rodoviaria(**kwargs)

    mock_rodoviaria_client.assert_called_once_with(
        kwargs["travel_ids"], kwargs["buseiro_ids"], kwargs["status_passagens"], kwargs["with_preco_rodoviaria"]
    )


def test_hard_stop_fechar_todos_grupos_classe_empresa():
    company = baker.make("core.Company")
    grupo = baker.make(
        "core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE, company=company, datetime_ida=timezone.now()
    )
    grupo_classe = baker.make("core.GrupoClasse", grupo=grupo)

    with mock.patch("core.service.rodoviaria_svc.list_empresas_marketplace_ids") as mock_empresas_marketplace_ids:
        mock_empresas_marketplace_ids.return_value = [company.id]
        rodoviaria_svc._fechar_todos_grupos_classe_empresa_hard_stop(company.id, "mensagem")

    grupo_classe.refresh_from_db()
    assert grupo_classe.closed
    assert grupo_classe.closed_reason == "Hard Stop - mensagem"
    assert grupo_classe.closed_by is None


def test_fechar_todos_grupos_classe_empresa_hard_stop_nao_integrada():
    company = baker.make("core.Company")
    grupo = baker.make(
        "core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE, company=company, datetime_ida=timezone.now()
    )
    baker.make("core.GrupoClasse", grupo=grupo)

    with (
        mock.patch("core.service.rodoviaria_svc.list_empresas_marketplace_ids") as mock_empresas_marketplace_ids,
        pytest.raises(RodoviariaException, match=r"Empresa não integrada. Não é possível fechar os trechos"),
    ):
        mock_empresas_marketplace_ids.return_value = [company.id + 1]
        rodoviaria_svc._fechar_todos_grupos_classe_empresa_hard_stop(company.id, "mensagem")


def test_hard_stop_empresa_logs(user_staff):
    company = baker.make("core.Company")
    grupo = baker.make(
        "core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE, company=company, datetime_ida=timezone.now()
    )
    baker.make("core.GrupoClasse", grupo=grupo)
    baker.make("core.GrupoClasse", grupo=grupo)
    with (
        mock.patch("core.service.rodoviaria_svc.list_empresas_marketplace_ids") as mock_empresas_marketplace_ids,
        mock.patch("core.service.rodoviaria_svc.remove_features_empresa"),
    ):
        mock_empresas_marketplace_ids.return_value = [company.id]
        rodoviaria_svc.hard_stop_empresa(company.id, "mensagem", user_staff)
    assert len(ActivityLog.objects.filter(type="rodoviaria_hard_stop_grupo")) == 2
    logs_companie = ActivityLog.objects.filter(type="rodoviaria_hard_stop_company").values()[0]
    logs_grupo = ActivityLog.objects.filter(type="rodoviaria_hard_stop_grupo").first()
    assert logs_companie["type"] == "rodoviaria_hard_stop_company"
    assert logs_companie["jsondata"] == json.dumps({"mensagem_fechamento": "mensagem", "qtd_grupos_fechados": 2})
    assert logs_companie["logged_user_id"] == user_staff.id
    assert logs_grupo is not None
    assert logs_grupo.type == "rodoviaria_hard_stop_grupo"
    assert logs_grupo.logged_user_id == user_staff.id
    assert logs_grupo.company_id == company.id
    assert logs_grupo.grupo_id == grupo.id
    assert logs_grupo.jsondata == json.dumps({"mensagem_fechamento": "mensagem"})


def test_simula_mover_buseiro_marketplace_menos_de_3h_para_viagem_origem_integrada(mock_is_integrado, time_machine):
    time_machine.move_to(to_default_tz_required("2022-08-12 14:04:54"))
    grupo = baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    trecho_classe = baker.make(TrechoClasse, grupo=grupo, datetime_ida=timezone.now() + timedelta(hours=1))
    reservation_code = "ABCDEF"
    travels = [baker.make(Travel, grupo=grupo, trecho_classe=trecho_classe, reservation_code=reservation_code)]
    trecho_classe_destino = baker.make(
        TrechoClasse,
        datetime_ida=timezone.now() + timedelta(hours=4),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    with pytest.raises(RodoviariaMoverBuseiroException) as ex:
        rodoviaria_svc.simula_mover_buseiro_marketplace(travels, trecho_classe_destino)
    assert (
        error_str(ex.value)
        == f"Travels {[reservation_code]} não podem ser canceladas pois estão há menos de 3h para viagem. (mover_buseiro_bloqueado)"
    )


def test_simula_mover_buseiro_marketplace_menos_de_3h_para_viagem_origem_nao_integrada(mocker, time_machine):
    time_machine.move_to(to_default_tz_required("2022-08-12 14:04:54"))
    mocker.patch("core.service.rodoviaria_svc._is_integrado", side_effect=[False, True, True])
    mocker.patch("core.service.rodoviaria_svc.grupo_nao_atualiza_preco", return_value=False)
    grupo = baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    trecho_classe = baker.make(TrechoClasse, grupo=grupo, datetime_ida=timezone.now() + timedelta(hours=1))
    reservation_code = "ABCDEF"
    travels = [
        baker.make(Travel, grupo=grupo, trecho_classe=trecho_classe, reservation_code=reservation_code, count_seats=1)
    ]
    trecho_classe_destino = baker.make(
        TrechoClasse,
        datetime_ida=timezone.now() + timedelta(hours=4),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        grupo_classe__tipo_assento="Executivo",
    )
    with (
        mock.patch.object(
            MarketplaceSeatsController,
            "get_layout_onibus",
            return_value=MapaPoltronasOnibus(
                layout=[
                    Deck(
                        andar=1,
                        assentos=[
                            Assento(livre=True, tipo_assento="Executivo", numero=1, x=0, y=0),
                            Assento(livre=False, tipo_assento="Executivo", numero=2, x=0, y=0),
                            Assento(livre=True, tipo_assento="Executivo", numero=3, x=0, y=0),
                        ],
                    )
                ]
            ),
        ),
        mock.patch(
            "core.service.rodoviaria_svc._client.update_trecho_classe_integracao",
            return_value={
                str(trecho_classe_destino.id): {"status": "integracao_ok", "vagas": 2, "preco_rodoviaria": 65}
            },
        ) as mock_update_trecho,
    ):
        rodoviaria_svc.simula_mover_buseiro_marketplace(travels, trecho_classe_destino)
    mock_update_trecho.assert_called_once_with(trecho_classe_id=trecho_classe_destino.id)
    assert trecho_classe_destino.max_split_value == D("65")
    assert trecho_classe_destino.vagas == 2


def test_simula_mover_buseiro_marketplace_origem_e_destino_nao_integradas(mocker, time_machine):
    time_machine.move_to(to_default_tz_required("2022-08-12 14:04:54"))
    mocker.patch("core.service.rodoviaria_svc._is_integrado", return_value=False)
    mocker.patch("core.service.rodoviaria_svc.grupo_nao_atualiza_preco", return_value=False)
    grupo = baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    trecho_classe = baker.make(TrechoClasse, grupo=grupo, datetime_ida=timezone.now() + timedelta(hours=1))
    reservation_code = "ABCDEF"
    travels = [
        baker.make(Travel, grupo=grupo, trecho_classe=trecho_classe, reservation_code=reservation_code, count_seats=1)
    ]
    trecho_classe_destino = baker.make(
        TrechoClasse,
        datetime_ida=timezone.now() + timedelta(hours=4),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    with mock.patch("core.service.rodoviaria_svc._client.update_trecho_classe_integracao") as mock_update_trecho:
        rodoviaria_svc.simula_mover_buseiro_marketplace(travels, trecho_classe_destino)
    mock_update_trecho.assert_not_called()


def test_simula_mover_buseiro_marketplace_destino_nao_integrada(mocker, time_machine):
    time_machine.move_to(to_default_tz_required("2022-08-12 14:04:54"))
    mocker.patch("core.service.rodoviaria_svc._is_integrado", side_effect=[True, False])
    mocker.patch("core.service.rodoviaria_svc.grupo_nao_atualiza_preco", return_value=False)
    grupo = baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    trecho_classe = baker.make(TrechoClasse, grupo=grupo, datetime_ida=timezone.now() + timedelta(hours=4))
    reservation_code = "ABCDEF"
    travels = [
        baker.make(Travel, grupo=grupo, trecho_classe=trecho_classe, reservation_code=reservation_code, count_seats=1)
    ]
    trecho_classe_destino = baker.make(
        TrechoClasse,
        datetime_ida=timezone.now() + timedelta(hours=4),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    with mock.patch("core.service.rodoviaria_svc._client.update_trecho_classe_integracao") as mock_update_trecho:
        rodoviaria_svc.simula_mover_buseiro_marketplace(travels, trecho_classe_destino)
    mock_update_trecho.assert_not_called()


def test_simula_mover_buseiro_marketplace_integracao_nao_encontrada(mock_is_integrado, time_machine):
    time_machine.move_to(to_default_tz_required("2022-08-12 14:04:54"))
    grupo = baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    trecho_classe = baker.make(TrechoClasse, grupo=grupo, datetime_ida=timezone.now() + timedelta(hours=4))
    reservation_code = "ABCDEF"
    travels = [baker.make(Travel, grupo=grupo, trecho_classe=trecho_classe, reservation_code=reservation_code)]
    trecho_classe_destino = baker.make(
        TrechoClasse,
        datetime_ida=timezone.now() + timedelta(hours=4),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    with (
        mock.patch(
            "core.service.rodoviaria_svc.update_status_integracao_trecho_classe",
            return_value={
                str(trecho_classe_destino.id): {
                    "status": "integracao_nao_encontrada",
                    "error": "Serviço não encontrado na API",
                }
            },
        ) as mock_update_trecho,
        pytest.raises(RodoviariaMoverBuseiroException, match="Serviço não encontrado na API"),
    ):
        rodoviaria_svc.simula_mover_buseiro_marketplace(travels, trecho_classe_destino)
    mock_update_trecho.assert_called_once_with(trecho_classe_destino)


def test_simula_mover_buseiro_marketplace_sem_vagas_suficientes(mocker, time_machine):
    time_machine.move_to(to_default_tz_required("2022-08-12 14:04:54"))
    mocker.patch("core.service.rodoviaria_svc._is_integrado", side_effect=[False, True, True])
    grupo = baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    trecho_classe = baker.make(TrechoClasse, grupo=grupo, datetime_ida=timezone.now() + timedelta(hours=4))
    reservation_code = "ABCDEF"
    travels = [
        baker.make(Travel, grupo=grupo, trecho_classe=trecho_classe, reservation_code=reservation_code, count_seats=3)
    ]
    trecho_classe_destino = baker.make(
        TrechoClasse,
        datetime_ida=timezone.now() + timedelta(hours=4),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        grupo_classe__tipo_assento="Executivo",
    )
    with (
        mock.patch(
            "core.service.rodoviaria_svc.update_status_integracao_trecho_classe",
            return_value={str(trecho_classe_destino.id): {"status": "integracao_ok"}},
        ) as mock_update_trecho,
        mock.patch.object(
            MarketplaceSeatsController,
            "get_layout_onibus",
            return_value=MapaPoltronasOnibus(
                layout=[
                    Deck(
                        andar=1,
                        assentos=[
                            Assento(livre=True, tipo_assento="Executivo", numero=1, x=0, y=0),
                            Assento(livre=False, tipo_assento="Executivo", numero=2, x=0, y=0),
                            Assento(livre=True, tipo_assento="Executivo", numero=3, x=0, y=0),
                        ],
                    )
                ]
            ),
        ),
        pytest.raises(RodoviariaMoverBuseiroException, match="Apenas 2 vagas disponíveis"),
    ):
        rodoviaria_svc.simula_mover_buseiro_marketplace(travels, trecho_classe_destino)
    mock_update_trecho.assert_called_once_with(trecho_classe_destino)


def test_simula_mover_buseiro_marketplace_sem_vagas_suficientes_devido_a_remanejamentos_pendentes(mocker, time_machine):
    time_machine.move_to(to_default_tz_required("2022-08-12 14:04:54"))
    mocker.patch("core.service.rodoviaria_svc._is_integrado", side_effect=[False, True, True])
    grupo = baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    trecho_classe = baker.make(TrechoClasse, grupo=grupo, datetime_ida=timezone.now() + timedelta(hours=4))
    reservation_code = "ABCDEF"
    travels = [
        baker.make(Travel, grupo=grupo, trecho_classe=trecho_classe, reservation_code=reservation_code, count_seats=3)
    ]
    trecho_classe_destino = baker.make(
        TrechoClasse,
        datetime_ida=timezone.now() + timedelta(hours=4),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        grupo_classe__tipo_assento="Executivo",
    )
    baker.make(
        Passageiro,
        travel=baker.make(Travel, trecho_classe=trecho_classe_destino, status="pending"),
        removed=False,
        poltrona=1,
        extra={},
        _quantity=2,
    )
    with (
        mock.patch(
            "core.service.rodoviaria_svc.update_status_integracao_trecho_classe",
            return_value={str(trecho_classe_destino.id): {"status": "integracao_ok", "vagas": 4}},
        ) as mock_update_trecho,
        mock.patch.object(
            MarketplaceSeatsController,
            "get_layout_onibus",
            return_value=MapaPoltronasOnibus(
                layout=[
                    Deck(
                        andar=1,
                        assentos=[
                            Assento(livre=True, tipo_assento="Executivo", numero=1, x=0, y=0),
                            Assento(livre=True, tipo_assento="Executivo", numero=2, x=0, y=0),
                            Assento(livre=True, tipo_assento="Executivo", numero=3, x=0, y=0),
                            Assento(livre=True, tipo_assento="Executivo", numero=4, x=0, y=0),
                        ],
                    )
                ]
            ),
        ),
        pytest.raises(
            RodoviariaMoverBuseiroException,
            match="Apenas 2 vagas disponíveis, pois há 2 reservas pendentes de emissão.",
        ),
    ):
        rodoviaria_svc.simula_mover_buseiro_marketplace(travels, trecho_classe_destino)
    mock_update_trecho.assert_called_once_with(trecho_classe_destino)


def test_simula_mover_buseiro_marketplace_sem_vagas_suficientes_no_check_mapa_poltronas(mocker, time_machine):
    time_machine.move_to(to_default_tz_required("2022-08-12 14:04:54"))
    mocker.patch("core.service.rodoviaria_svc._is_integrado", side_effect=[False, True])
    grupo = baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    trecho_classe = baker.make(TrechoClasse, grupo=grupo, datetime_ida=timezone.now() + timedelta(hours=4))
    reservation_code = "ABCDEF"
    travels = [
        baker.make(Travel, grupo=grupo, trecho_classe=trecho_classe, reservation_code=reservation_code, count_seats=3)
    ]
    trecho_classe_destino = baker.make(
        TrechoClasse,
        datetime_ida=timezone.now() + timedelta(hours=4),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
    )
    with (
        mock.patch(
            "core.service.rodoviaria_svc.update_status_integracao_trecho_classe",
            return_value={str(trecho_classe_destino.id): {"status": "integracao_ok", "vagas": 5}},
        ) as mock_update_trecho,
        mock.patch("core.service.rodoviaria_svc._client.remanejamentos_pendentes", return_value={"remanejamentos": []}),
        mock.patch(
            "core.service.rodoviaria_svc.get_map_poltronas", return_value={"1": "livre", "2": "livre", "3": "ocupada"}
        ) as mock_map_poltronas,
        pytest.raises(RodoviariaMoverBuseiroException, match="Apenas 2 vagas disponíveis"),
    ):
        rodoviaria_svc.simula_mover_buseiro_marketplace(travels, trecho_classe_destino)
    mock_update_trecho.assert_called_once_with(trecho_classe_destino)
    mock_map_poltronas.assert_called_once_with(trecho_classe_destino.id)


def test_simula_mover_buseiro_marketplace_update_vagas_origem_e_destino_integradas(
    mock_is_integrado, time_machine, mocker
):
    mocker.patch("core.service.rodoviaria_svc.grupo_nao_atualiza_preco", return_value=False)
    time_machine.move_to(to_default_tz_required("2022-08-12 14:04:54"))
    grupo = baker.make(Grupo, modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
    trecho_classe = baker.make(TrechoClasse, grupo=grupo, datetime_ida=timezone.now() + timedelta(hours=4))
    reservation_code = "ABCDEF"
    travels = [
        baker.make(Travel, grupo=grupo, trecho_classe=trecho_classe, reservation_code=reservation_code, count_seats=1)
    ]
    trecho_classe_destino = baker.make(
        TrechoClasse,
        datetime_ida=timezone.now() + timedelta(hours=4),
        max_split_value=D("100"),
        grupo__modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        grupo_classe__tipo_assento="Executivo",
    )
    with (
        mock.patch(
            "core.service.rodoviaria_svc._client.update_trecho_classe_integracao",
            return_value={
                str(trecho_classe_destino.id): {"status": "integracao_ok", "vagas": 2, "preco_rodoviaria": 65}
            },
        ) as mock_update_trecho,
        mock.patch.object(
            MarketplaceSeatsController,
            "get_layout_onibus",
            return_value=MapaPoltronasOnibus(
                layout=[
                    Deck(
                        andar=1,
                        assentos=[
                            Assento(livre=True, tipo_assento="Executivo", numero=1, x=0, y=0),
                            Assento(livre=False, tipo_assento="Executivo", numero=2, x=0, y=0),
                            Assento(livre=True, tipo_assento="Executivo", numero=3, x=0, y=0),
                        ],
                    )
                ]
            ),
        ),
    ):
        rodoviaria_svc.simula_mover_buseiro_marketplace(travels, trecho_classe_destino)
    mock_update_trecho.assert_called_once_with(trecho_classe_id=trecho_classe_destino.id)
    assert trecho_classe_destino.max_split_value == D("65")
    assert trecho_classe_destino.vagas == 2


def test_quantidade_remanejamentos_pendentes_grupo():
    grupo_id = 3123
    with mock.patch(
        "core.service.rodoviaria_svc._client.remanejamentos_pendentes",
        return_value={"remanejamentos": [{"count_seats": 2}, {"count_seats": 4}]},
    ) as mock_client:
        qtd_remanejamentos_pendentes = rodoviaria_svc.quantidade_remanejamentos_pendentes_grupo(grupo_id)
    assert qtd_remanejamentos_pendentes == 6
    mock_client.assert_called_once_with(grupo_id)


def test_grupos_todos_emitiram(mocker, mock_bulk_grupo_tem_passagem_emitida):
    grupo = baker.make("core.grupo")
    travel = baker.make("core.Travel", grupo=grupo)
    buseiro = baker.make("core.Buseiro")
    baker.make("core.Passageiro", travel=travel, buseiro=buseiro)

    grupos_com_bpe = rodoviaria_svc.grupos_com_bpe_emitido(Grupo.objects.filter(id=grupo.id))
    assert grupos_com_bpe.count() == 1
    assert grupos_com_bpe[0].id == grupo.id


def test_top_trechos_com_divergencia():
    with mock.patch("core.service.rodoviaria_svc._client.top_trechos_com_divergencia") as mock_client:
        rodoviaria_svc.top_trechos_com_divergencia(days_behind=7, quantity=100)
    mock_client.assert_called_once_with(7, 100)


def test_get_passagem_info():
    travel_id = 1
    buseiro_id = 2
    rodoviaria_response = [{"numero_passagem": 12233}]
    with mock.patch(
        "core.service.rodoviaria_svc._client.get_passagem_info", return_value=rodoviaria_response
    ) as mock_client:
        response = rodoviaria_svc.get_passagem_info(travel_id=travel_id, buseiro_id=buseiro_id)
    mock_client.assert_called_once_with(travel_id, buseiro_id)
    assert response == rodoviaria_response


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_log_cancel_travel_by_user_request_error_marketplace(travels_mock):
    travel = travels_mock[0]
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._is_prod") as _is_prod_mock,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as _is_integrado_mock,
    ):
        _is_prod_mock.return_value = True
        _is_integrado_mock.return_value = True
        efetua_cancelamento_mock.side_effect = PassengerTicketAlreadyPrintedException()
        with pytest.raises(PassengerTicketAlreadyPrintedException):
            rodoviaria_svc.efetua_cancelamento(travel)
        log = (
            ActivityLog.objects.filter(type="rodoviaria_error_falha_no_cancelamento_marketplace")
            .only("id", "type", "jsondata", "touser", "travel")
            .first()
        )
        assert log is not None and log.jsondata is not None
        log_json = json.loads(log.jsondata)
        assert log_json == {
            "error": "Sua passagem já está impressa e não pode ser cancelada automaticamente. (rodoviaria_passenger_ticket_already_printed)"
        }
        assert log.touser == travel.user
        assert log.travel == travel


@time_machine.travel(to_default_tz_required(datetime(2022, 1, 10, 14, 32)))
def test_log_cancel_travel_changed_seat_raise_error(travels_mock):
    travel = travels_mock[0]
    with (
        mock.patch("core.service.rodoviaria_svc._client.efetua_cancelamento") as efetua_cancelamento_mock,
        mock.patch("core.service.rodoviaria_svc._is_prod") as _is_prod_mock,
        mock.patch("core.service.rodoviaria_svc._is_integrado") as _is_integrado_mock,
    ):
        _is_prod_mock.return_value = True
        _is_integrado_mock.return_value = True
        efetua_cancelamento_mock.side_effect = PoltronaTrocadaException()
        with pytest.raises(PoltronaTrocadaException):
            rodoviaria_svc.efetua_cancelamento(travel)
        log = (
            ActivityLog.objects.filter(type="rodoviaria_error_falha_no_cancelamento_marketplace")
            .only("id", "type", "jsondata", "touser", "travel")
            .first()
        )
        assert log is not None, "log não deve ser None"
        assert log.jsondata is not None, "log.jsondata não deve ser None"
        assert log.jsondata_dict is not None, "log.jsondata_dict não deve ser None"
        log_dict = log.jsondata_dict
        assert (
            log_dict["error"]
            == "Algo deu errado na comunicação com a empresa (rodoviaria_passenger_ticket_seat_changed)"
        )
        assert log_dict["type"] == "rodoviaria_passenger_ticket_seat_changed"
        assert log.touser == travel.user
        assert log.travel == travel


def test_get_informacoes_passagem_api_parceiro():
    buseiro_id = 2
    modelo_venda = "marketplace"
    travel_id = 1
    rodoviaria_response = {"numero_passagem": 6666}
    with mock.patch(
        "core.service.rodoviaria_svc.get_informacoes_passagem_api_parceiro",
        return_value={"numero_passagem": 6666},
    ) as mock_client:
        response = rodoviaria_svc.get_informacoes_passagem_api_parceiro(buseiro_id, modelo_venda, travel_id)
    mock_client.assert_called_once_with(buseiro_id, modelo_venda, travel_id)
    assert response == rodoviaria_response


def test_abrir_todos_grupos_classe_empresa_revert_hard_stop(user_staff):
    company = baker.make("core.Company")
    grupo = baker.make(
        "core.Grupo",
        modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        company=company,
        datetime_ida=timezone.now() + timedelta(days=2),
    )
    grupo_classe_deve_abrir = baker.make(
        "core.GrupoClasse", grupo=grupo, closed=True, closed_reason=f"{rodoviaria_svc.HARD_STOP_PREFIX} mensagem"
    )
    grupo_classe_manter_fechado = baker.make("core.GrupoClasse", grupo=grupo, closed=True, closed_reason="Mensagem")
    rodoviaria_svc._abrir_todos_grupos_classe_empresa_revert_hard_stop(company.id, user_staff)
    grupo_classe_deve_abrir.refresh_from_db()
    grupo_classe_manter_fechado.refresh_from_db()
    assert len(GrupoClasse.objects.filter(closed=True))
    assert not grupo_classe_deve_abrir.closed
    assert not grupo_classe_deve_abrir.closed_reason
    assert grupo_classe_deve_abrir.closed_by is None
    assert grupo_classe_manter_fechado.closed is True
    assert grupo_classe_manter_fechado.closed_reason


def test_nao_abre_grupos_classe_empresa_revert_hard_stop_passado(user_staff):
    company = baker.make("core.Company")
    grupo = baker.make(
        "core.Grupo",
        modelo_venda=Grupo.ModeloVenda.MARKETPLACE,
        company=company,
        datetime_ida=timezone.now() - timedelta(days=1),
    )
    grupo_classe = baker.make(
        "core.GrupoClasse",
        grupo=grupo,
        closed=True,
        closed_reason=f"{rodoviaria_svc.HARD_STOP_PREFIX} mensagem",
        closed_by=user_staff,
    )
    rodoviaria_svc._abrir_todos_grupos_classe_empresa_revert_hard_stop(company.id, user_staff)
    grupo_classe.refresh_from_db()
    assert grupo_classe.closed is True
    assert grupo_classe.closed_reason == f"{rodoviaria_svc.HARD_STOP_PREFIX} mensagem"
    assert grupo_classe.closed_by == user_staff


def test_raise_if_unable_hard_stop_error(user_staff):
    company = baker.make("core.Company")
    grupo = baker.make(
        "core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE, company=company, datetime_ida=timezone.now()
    )
    grupo_classe = baker.make(
        "core.GrupoClasse",
        grupo=grupo,
        closed=True,
        closed_reason="AAA bla bla bla",
        closed_by=user_staff,
    )
    msg_erro = "Essa empresa não é integrada"
    with mock.patch("core.service.rodoviaria_svc.raise_if_unable_hard_stop") as mock_raise_if_unable_hard_stop:
        mock_raise_if_unable_hard_stop.side_effect = RodoviariaException(message=msg_erro)
        with pytest.raises(RodoviariaException, match=msg_erro):
            rodoviaria_svc.revert_hardstop_empresa(company.id, user_staff)
    assert grupo_classe.closed is True
    assert grupo_classe.closed_reason is not None
    assert grupo_classe.closed_by == user_staff


def test_revert_hard_stop_empresa_logs(user_staff):
    company = baker.make("core.Company")
    grupo = baker.make(
        "core.Grupo", modelo_venda=Grupo.ModeloVenda.MARKETPLACE, company=company, datetime_ida=timezone.now()
    )
    baker.make(
        "core.GrupoClasse",
        grupo=grupo,
        closed=True,
        closed_reason=f"{rodoviaria_svc.HARD_STOP_PREFIX} mensagem",
        closed_by=user_staff,
    )
    baker.make(
        "core.GrupoClasse",
        grupo=grupo,
        closed=True,
        closed_reason=f"{rodoviaria_svc.HARD_STOP_PREFIX} mensagem",
        closed_by=user_staff,
    )
    rodoviaria_svc._abrir_todos_grupos_classe_empresa_revert_hard_stop(company.id, user_staff)
    assert len(ActivityLog.objects.filter(type="rodoviaria_hard_stop_revert_grupo")) == 2
    logs_companie = ActivityLog.objects.filter(type="rodoviaria_hard_stop_revert_company").values()[0]
    logs_grupo = ActivityLog.objects.filter(type="rodoviaria_hard_stop_revert_grupo").first()
    assert logs_companie["type"] == "rodoviaria_hard_stop_revert_company"
    assert logs_companie["logged_user_id"] == user_staff.id
    assert logs_companie["company_id"] == company.id
    assert logs_companie["jsondata"] == json.dumps({"qtd_grupos_reabertos": 2})
    assert logs_grupo is not None, "logs_grupo should not be None"
    assert logs_grupo.type == "rodoviaria_hard_stop_revert_grupo"
    assert logs_grupo.logged_user_id == user_staff.id
    assert logs_grupo.company_id == company.id
    assert logs_grupo.grupo_id == grupo.id


def test_busca_viagens_api_na_search_sem_origem_destino_data_ida():
    origem = CidadeAdapter(baker.make(Cidade))
    destino = CidadeAdapter(baker.make(Cidade))
    data_ida = datetime(2024, 6, 27)
    # Como o objetivo é testar a função com parametros None, o ignore é necessário
    assert rodoviaria_svc.busca_viagens_api_na_search(origem, destino, None) is None  # type: ignore
    assert rodoviaria_svc.busca_viagens_api_na_search(origem, None, data_ida) is None  # type: ignore
    assert rodoviaria_svc.busca_viagens_api_na_search(None, destino, data_ida) is None  # type: ignore


def test_busca_viagens_api_na_search_fora_da_margem(mocker, globalsettings_mock):
    origem = CidadeAdapter(baker.make(Cidade))
    destino = CidadeAdapter(baker.make(Cidade))
    data_ida = datetime(2024, 6, 27)
    globalsettings_mock("percentual_search_para_buscar_na_api_rodoviaria", 10)
    mocker.patch("core.service.rodoviaria_svc.random.random", return_value=0.2)
    assert rodoviaria_svc.busca_viagens_api_na_search(origem, destino, data_ida) is None


def test_busca_viagens_api_na_search(mocker, globalsettings_mock):
    origem = baker.make(Cidade)
    destino = baker.make(Cidade)
    data_ida = datetime(2024, 6, 27)
    globalsettings_mock("percentual_search_para_buscar_na_api_rodoviaria", 10)
    mocker.patch("core.service.rodoviaria_svc.random.random", return_value=0.05)
    mock_client = mocker.patch.object(RodoviariaClient, "buscar_viagens_todas_empresas_api_na_search", return_value={})
    assert rodoviaria_svc.busca_viagens_api_na_search(CidadeAdapter(origem), CidadeAdapter(destino), data_ida) is True
    mock_client.assert_called_once_with(origem.id, destino.id, date(2024, 6, 27))
