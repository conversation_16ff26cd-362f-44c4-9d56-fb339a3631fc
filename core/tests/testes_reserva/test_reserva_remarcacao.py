import copy
from datetime import timedelta
from decimal import Decimal as D
from unittest import mock

from django.utils import timezone, translation
from django.utils.formats import date_format

from adapters.mock.mock_mercadopago_adapter import (
    CARTAO_MERCADO_DEFAULT,
    mock_mercadopago,
)
from adapters.mock.mock_payment_adapter import (
    BOLETO_STARK,
    create_stark_provider_mock,
)
from adapters.torre_de_controle_adapter.forms import BusTelemetryForm
from commons import dateutils, utils
from commons.dateutils import to_default_tz_required
from core.models_grupo import Grupo
from core.models_travel import Cupom, Travel
from core.service.selecao_assento.marketplace import MarketplaceSeatsController
from core.service.selecao_assento.models import Assento, BlockedSeat
from core.tests import fixtures
from core.tests.common_operations import assert_accounting
from core.tests.prevent_useless_logs import prevent_request_errors
from core.tests.testes_reserva.test_reserva_base import TestReservaBase


def mocked_torre_response_no_info():
    return BusTelemetryForm(
        bus_eta=BusTelemetryForm.BusEta(),
        bus_location=BusTelemetryForm.BusLocation(),
        bus_delayed_info=BusTelemetryForm.BusDelayedInfo(),
    )


class TestReservaRemarcacao(TestReservaBase):
    @classmethod
    def setUpTestData(cls):
        super(TestReservaRemarcacao, cls).setUpTestData()
        b = cls.b
        fixtures.grupo_classe_spbh(b)
        fixtures.grupo_classe_spbh(b, dtbase=dateutils.now() + timedelta(days=10))
        fixtures.grupo_classe_bhipa(b, dtbase=dateutils.now() + timedelta(days=15))

    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    @mock_mercadopago(value=D("176"))
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    @mock.patch("core.service.reserva.travel.travel_svc.atribuir_poltronas_automaticamente")
    def test_remarcacao_chama_atribuir_poltronas_automaticamente(self, mock_atribuir_poltronas, mock_torre, mock_n):
        client = self.client
        client.force_login(self.user_ze)

        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
        )

        grupo_id = self._get_grupo_bhsp()
        mock_torre.return_value = mocked_torre_response_no_info()

        travels_remarcadas = self.remarcar(client, travels[0]["id"], grupo_id)

        assert travels_remarcadas is not None

        for travel in travels_remarcadas:
            mock_atribuir_poltronas.assert_any_call(Travel.objects.get(pk=travel["id"]))

    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    @mock_mercadopago(value=D("176"))
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    def test_remarcacao_simples(self, mock_torre, mock_n):
        client = self.client
        client.force_login(self.user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta
        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
        )
        assert_accounting(client, count=3, saldo_real=D(0))

        # Vejo o extrato de remarcação da ida
        grupo_id = self._get_grupo_bhsp()
        extrato_esperado = dict(
            extrato_cancelamento=dict(
                title="Reserva anterior",
                entries=[
                    dict(title="Cancelada", value=valor_individual),
                    dict(title="Saldo devolvido na carteira", value=valor_individual),
                ],
            ),
            extrato=[
                dict(
                    title="Extrato ida",
                    entries=[
                        dict(title="Reserva ida", subtitle="1 passageiro leito", value=valor_individual),
                        dict(
                            title="Taxa de revenda",
                            subtitle="1 passageiro",
                            value=D("0"),
                            type="taxa_revenda",
                            desconto_taxa_servico=D("0"),
                        ),
                        dict(title="Saldo utilizado", value=-valor_individual),
                    ],
                    footer=dict(title="Total reserva ida", value=D(0)),
                ),
                dict(title="Total a pagar", value=D(0)),
                dict(title="Total ida", value=D(0)),
                dict(title="Total volta", value=D(0)),
            ],
        )
        mock_torre.return_value = mocked_torre_response_no_info()
        self._get_and_assert_extrato_remarcacao(client, travels[0]["id"], grupo_id, extrato_esperado)

        # Faço a remarcação
        travels_remarcadas = self.remarcar(client, travels[0]["id"], grupo_id)

        if not travels_remarcadas:
            raise ValueError("Algo deu errado na remarcação")

        travel_remarcada_id = travels_remarcadas[0]["id"]
        travel_remarcada = Travel.objects.get(pk=travel_remarcada_id)
        self.assertIsNotNone(travel_remarcada.reserva)
        self.assertIsNotNone(travel_remarcada.reserva_id)
        assert_accounting(client, count=5, saldo_real=D(0), saldo_bueda=D(0))

    @mock_mercadopago(value=D("176"))
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    def test_remarcacao_nao_aplica_promocao_move_travel(self, mock_torre):
        client = self.client
        client.force_login(self.user_ze)
        cupom = Cupom.objects.create(
            code="CUPOM",
            discount=D(".5"),
            is_volta=False,
            type="PROMO",
            due_date=to_default_tz_required(dateutils.now() + timedelta(days=3)),
        )

        # Valores
        valor_individual = D("88.0")
        cupom_value = valor_individual * D(".5")
        pay_value = valor_individual - cupom_value

        # Reserva ida
        travel = self.entra_no_grupo_soida(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX, cupom="CUPOM"
        )

        assert_accounting(client, count=3, saldo_real=D(0), saldo_bueda=D(0))

        # Vejo o extrato de remarcação
        grupo_id = self._get_grupo_bhsp()
        extrato_esperado = dict(
            extrato_cancelamento=dict(
                title="Reserva anterior",
                entries=[
                    dict(title="Cancelada", value=pay_value),
                    dict(title="Saldo devolvido na carteira", value=pay_value),
                ],
            ),
            extrato=[
                dict(
                    title="Extrato ida",
                    entries=[
                        dict(title="Reserva ida", subtitle="1 passageiro leito", value=valor_individual),
                        dict(
                            title="Taxa de revenda",
                            subtitle="1 passageiro",
                            value=D("0"),
                            type="taxa_revenda",
                            desconto_taxa_servico=D("0"),
                        ),
                        dict(title="Promoção", subtitle=f"Cupom ({cupom.code})", value=-cupom_value),
                        dict(title="Saldo utilizado", value=-pay_value),
                    ],
                    footer=dict(title="Total reserva ida", value=D(0)),
                ),
                dict(title="Total a pagar", value=D(0)),
                dict(title="Total ida", value=D(0)),
                dict(title="Total volta", value=D(0)),
            ],
        )
        mock_torre.return_value = mocked_torre_response_no_info()
        self._get_and_assert_extrato_remarcacao(client, travel["id"], grupo_id, extrato_esperado)

    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    @mock_mercadopago(value=176)
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    def test_remarcacao_simples_tenta_remarcar_duas_vezes_seguidas(self, mock_torre, mock_n):
        client = self.client
        client.force_login(self.user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta
        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
        )

        grupo_id = self._get_grupo_bhsp()
        mock_torre.return_value = mocked_torre_response_no_info()
        # Faço a remarcação
        self.remarcar(client, travels[0]["id"], grupo_id)
        self.remarcar(client, travels[0]["id"], grupo_id, expecterror="Essa viagem já foi cancelada")

    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    @mock_mercadopago(value=176)
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    def test_remarcacao_com_pax_removido_mantem_count_seats_correto(self, mock_torre, mock_n):
        client = self.client
        client.force_login(self.user_ze)

        travel = self.entra_no_grupo_n_pessoas(client, payment=CARTAO_MERCADO_DEFAULT, pessoas=2)
        travel_db = Travel.objects.get(id=travel["id"])
        travel_db.refresh_from_db(fields=["count_seats"])
        self.assertEqual(travel_db.count_seats, 2)

        pid = travel["passengers"][-1]["pid"]

        # remove 1 pax
        client.post(f"/api/removepassenger/{pid}")
        travel_db.refresh_from_db(fields=["count_seats"])
        self.assertEqual(travel_db.count_seats, 1)

        grupo_id = self._get_grupo_bhsp()
        mock_torre.return_value = mocked_torre_response_no_info()
        travels = self.remarcar(client, travel["id"], grupo_id)
        assert travels
        new_travel = Travel.objects.get(id=travels[0]["id"])
        self.assertEqual(new_travel.count_seats, 1)

    @mock_mercadopago(value=176)
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    def test_remarcacao_nao_deixa_origem_diferente(self, mock_torre):
        client = self.client
        client.force_login(self.user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta
        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
        )
        assert_accounting(client, count=3, saldo_real=D(0))

        # Tento remarcar com uma origem diferente
        grupo_id = self._get_grupo_bhsp(ida=False)
        erro_esperado = "Nova origem diferente do travel anterior"
        mock_torre.return_value = mocked_torre_response_no_info()
        self._get_and_assert_extrato_remarcacao(
            client, travels[0]["id"], grupo_id, None, error=erro_esperado, status_code=400
        )
        self.remarcar(
            client,
            travels[0]["id"],
            grupo_id,
            payment=CARTAO_MERCADO_DEFAULT,
            pay=D(0),
            expecterror=erro_esperado,
        )

    @mock_mercadopago(value=176)
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    def test_remarcacao_nao_deixa_destino_diferente(self, mock_torre):
        client = self.client
        client.force_login(self.user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta
        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
        )
        assert_accounting(client, count=3, saldo_real=D("0"))
        mock_torre.return_value = mocked_torre_response_no_info()
        # Tento remarcar com um destino diferente
        grupo_id = self._get_grupo_bhipa()
        erro_esperado = "Novo destino diferente do travel anterior"
        self._get_and_assert_extrato_remarcacao(
            client, travels[0]["id"], grupo_id, None, error=erro_esperado, status_code=400
        )
        self.remarcar(
            client,
            travels[0]["id"],
            grupo_id,
            payment=CARTAO_MERCADO_DEFAULT,
            pay=D(0),
            expecterror=erro_esperado,
        )

    @mock_mercadopago(value=176)
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    def test_remarcacao_nao_deixa_usuario_diferente(self, mock_torre):
        client = self.client
        user_ze = self.user_ze
        user_joao = fixtures.user_joao()
        client.force_login(user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta
        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
        )
        assert_accounting(client, count=3, saldo_real=D(0))

        # Tento o extrato de remarcação e a remarcação com um usuário diferente logado
        grupo_id = self._get_grupo_bhipa()
        erro_esperado = "SUSPEITO! extrato remarcação suspeito: %s, %s, %s" % (
            travels[0]["id"],
            user_ze.id,
            user_joao.id,
        )
        mock_torre.return_value = mocked_torre_response_no_info()
        client.force_login(user_joao)

        with prevent_request_errors():
            self._get_and_assert_extrato_remarcacao(client, travels[0]["id"], grupo_id, None, error=erro_esperado)
        erro_esperado = "SUSPEITO! remarcação suspeito: %s, %s, %s" % (travels[0]["id"], user_ze.id, user_joao.id)
        self.remarcar(
            client,
            travels[0]["id"],
            grupo_id,
            payment=CARTAO_MERCADO_DEFAULT,
            pay=D(0),
            expecterror=erro_esperado,
        )

    @mock.patch("core.payment.providers.stark.StarkProvider", create_stark_provider_mock())
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    def test_remarcacao_com_boleto_em_aberto(self, mock_torre):
        client = self.client
        client.force_login(self.user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta (não pago o boleto)
        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=BOLETO_STARK, pay=pay_value, passengers=ONE_PAX
        )
        assert_accounting(client, count=3, saldo_real=D(0), saldo_bueda=D(0))
        mock_torre.return_value = mocked_torre_response_no_info()
        # Não deixa remarcar com boleto pendente
        grupo_id = self._get_grupo_bhsp()
        self.remarcar(
            client,
            travels[0]["id"],
            grupo_id,
            payment=BOLETO_STARK,
            pay=D(0),
            expecterror="Não é possivel remarcar com boleto pendente",
        )

        assert_accounting(client, count=3, saldo_real=D(0), saldo_bueda=D(0))

    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    @mock_mercadopago(value=176)
    @mock.patch("core.service.notifications.user_notification_svc.pagamento_estornado")
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    def test_remarcacao_nao_deixa_travel_cancelado(self, mock_torre, mock_pagamento_estornado, mock_n):
        client = self.client
        client.force_login(self.user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta
        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
        )
        assert_accounting(client, count=3, saldo_real=D(0), saldo_bueda=D(0))

        # Cancelo o travel
        self.cancela_viagem(client, travels[0]["id"])
        mock_torre.return_value = mocked_torre_response_no_info()
        # Tento o extrato de remarcação e a remarcação com a travel cancelada
        grupo_id = self._get_grupo_bhsp()

        expect_error = "Essa viagem já foi cancelada"
        with prevent_request_errors():
            self._get_and_assert_extrato_remarcacao(
                client, travels[0]["id"], grupo_id, None, error=expect_error, status_code=400
            )
            self.remarcar(
                client,
                travels[0]["id"],
                grupo_id,
                payment=CARTAO_MERCADO_DEFAULT,
                pay=D(0),
                expecterror=expect_error,
            )

    @mock_mercadopago(value=176)
    @mock.patch("commons.feature_flags.is_user_enabled", lambda flag, user_id=None: flag == "pricing-promo")
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    def test_remarcacao_nova_viagem_valor_menor(self, mock_n, mock_torre):
        client = self.client
        client.force_login(self.user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta
        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
        )
        assert_accounting(client, count=3, saldo_real=D(0))

        # Vejo o extrato de remarcação da ida
        diff_valor = -D("10")
        novo_valor = valor_individual + diff_valor

        grupo_id = self._get_grupo_bhsp(max_split_value=novo_valor)
        extrato_esperado = dict(
            extrato_cancelamento=dict(
                title="Reserva anterior",
                entries=[
                    dict(title="Cancelada", value=valor_individual),
                    dict(title="Saldo devolvido na carteira", value=valor_individual),
                ],
            ),
            extrato=[
                dict(
                    title="Extrato ida",
                    entries=[
                        dict(title="Reserva ida", subtitle="1 passageiro leito", value=novo_valor),
                        dict(
                            title="Taxa de revenda",
                            subtitle="1 passageiro",
                            value=D("0"),
                            type="taxa_revenda",
                            desconto_taxa_servico=D("0"),
                        ),
                        dict(title="Saldo utilizado", value=-novo_valor),
                    ],
                    footer=dict(title="Total reserva ida", value=D(0)),
                ),
                dict(title="Total a pagar", value=D(0)),
                dict(title="Total ida", value=D(0)),
                dict(title="Total volta", value=D(0)),
            ],
        )
        mock_torre.return_value = mocked_torre_response_no_info()
        self._get_and_assert_extrato_remarcacao(client, travels[0]["id"], grupo_id, extrato_esperado)

        # Faço a remarcação
        self.remarcar(client, travels[0]["id"], grupo_id)

        assert_accounting(client, count=6, saldo_real=valor_individual - novo_valor)

    @mock_mercadopago(value=176)
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    def test_remarcacao_nova_viagem_valor_maior_diff_menor_igual_2_reais(self, mock_n, mock_torre):
        client = self.client
        client.force_login(self.user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta
        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
        )
        assert_accounting(client, count=3, saldo_real=D(0))

        # Vejo o extrato de remarcação da ida
        diff_valor = D("2")
        novo_valor = valor_individual + diff_valor
        falta_pagar = novo_valor - valor_individual

        grupo_id = self._get_grupo_bhsp(max_split_value=novo_valor)
        extrato_esperado = dict(
            extrato_cancelamento=dict(
                title="Reserva anterior",
                entries=[
                    dict(title="Cancelada", value=valor_individual),
                    dict(title="Saldo devolvido na carteira", value=valor_individual),
                ],
            ),
            extrato=[
                dict(
                    title="Extrato ida",
                    entries=[
                        dict(title="Reserva ida", subtitle="1 passageiro leito", value=novo_valor),
                        dict(
                            title="Taxa de revenda",
                            subtitle="1 passageiro",
                            value=D("0"),
                            type="taxa_revenda",
                            desconto_taxa_servico=D("0"),
                        ),
                        dict(title="Saldo utilizado", value=-valor_individual),
                    ],
                    footer=dict(title="Total reserva ida", value=falta_pagar),
                ),
                dict(title="Total a pagar", value=falta_pagar),
                dict(title="Total ida", value=falta_pagar),
                dict(title="Total volta", value=D(0)),
            ],
        )
        mock_torre.return_value = mocked_torre_response_no_info()
        self._get_and_assert_extrato_remarcacao(client, travels[0]["id"], grupo_id, extrato_esperado)

        # Faço a remarcação
        self.remarcar(client, travels[0]["id"], grupo_id)

        assert_accounting(client, count=5, saldo_real=valor_individual - novo_valor)

    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    def test_remarcacao_nova_viagem_valor_maior_diff_maior_2_reais(self, mock_n, mock_torre):
        client = self.client
        client.force_login(self.user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta
        with mock_mercadopago(value=176):
            travels = self.entra_no_grupo_ida_e_volta_separadas(
                client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
            )
        assert_accounting(client, count=3, saldo_real=D(0))

        # Vejo o extrato de remarcação da ida
        diff_valor = D("5")
        novo_valor = valor_individual + diff_valor
        falta_pagar = novo_valor - valor_individual

        grupo_id = self._get_grupo_bhsp(max_split_value=novo_valor)
        extrato_esperado = dict(
            extrato_cancelamento=dict(
                title="Reserva anterior",
                entries=[
                    dict(title="Cancelada", value=valor_individual),
                    dict(title="Saldo devolvido na carteira", value=valor_individual),
                ],
            ),
            extrato=[
                dict(
                    title="Extrato ida",
                    entries=[
                        dict(title="Reserva ida", subtitle="1 passageiro leito", value=novo_valor),
                        dict(
                            title="Taxa de revenda",
                            subtitle="1 passageiro",
                            value=D("0"),
                            type="taxa_revenda",
                            desconto_taxa_servico=D("0"),
                        ),
                        dict(title="Saldo utilizado", value=-valor_individual),
                    ],
                    footer=dict(title="Total reserva ida", value=falta_pagar),
                ),
                dict(title="Total a pagar", value=falta_pagar),
                dict(title="Total ida", value=falta_pagar),
                dict(title="Total volta", value=D(0)),
            ],
        )
        mock_torre.return_value = mocked_torre_response_no_info()
        self._get_and_assert_extrato_remarcacao(client, travels[0]["id"], grupo_id, extrato_esperado)

        # Faço a remarcação
        with mock_mercadopago(value=falta_pagar):
            self.remarcar(client, travels[0]["id"], grupo_id, payment=CARTAO_MERCADO_DEFAULT, pay=falta_pagar)

        # Checo o saldo
        assert_accounting(client, count=6, saldo_real=D(0))

    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    def test_remarcacao_e_extrato_com_cupom(self, mock_n, mock_torre):
        mock_torre.return_value = mocked_torre_response_no_info()
        CARTAO = CARTAO_MERCADO_DEFAULT.copy()
        CARTAO["value"] = D("132")
        client = self.client
        client.force_login(self.user_ze)

        CUPOM_CODE = "CUPOM_REMARCACAO"
        Cupom.objects.create(
            code=CUPOM_CODE,
            discount=D("0.50"),
            is_volta=True,
            type="PROMO",
            due_date=to_default_tz_required(dateutils.now() + timedelta(days=3)),
            datetime_ida_end=to_default_tz_required(dateutils.now() + timedelta(days=14)),
            is_apenas_reserva=True,
        )

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        valor_desconto_cupom = valor_individual * D("0.5")
        pay_value = valor_individual * count_seats - valor_desconto_cupom

        # Reserva ida e volta
        with mock_mercadopago(value=132):
            travels = self.entra_no_grupo_ida_e_volta_separadas(
                client, payment=CARTAO, pay=pay_value, passengers=ONE_PAX, cupom=CUPOM_CODE
            )
        assert_accounting(client, count=4, saldo_real=D(0), saldo_bueda=D(0))

        # Remarco a travel que ganhou a promoção do cupom, deve cobrar o mesmo valor
        grupo_id = self._get_grupo_bhsp()
        with mock_mercadopago(value=44):
            nova_ida = self.remarcar(client, travels[0]["id"], grupo_id, payment=CARTAO, pay=D(0))
        assert nova_ida
        assert_accounting(client, count=8, saldo_real=D(0), saldo_bueda=D(0))

        # Remarco a travel que não ganhou promoção do cupom, deve cobrar o mesmo valor
        grupo_id = self._get_grupo_bhsp(ida=False)
        with mock_mercadopago(value=88):
            self.remarcar(client, travels[1]["id"], grupo_id, payment=CARTAO, pay=D(0))
        assert_accounting(client, count=10, saldo_real=D(0), saldo_bueda=D(0))

        # Tento remarcar a ida para um data fora do range da promoção, pago a diferença
        grupo_id = self._get_grupo_bhsp(add_dias=10)
        falta_pagar = valor_individual - valor_desconto_cupom
        extrato_esperado = dict(
            extrato_cancelamento=dict(
                title="Reserva anterior",
                entries=[
                    dict(title="Cancelada", value=valor_desconto_cupom),
                    dict(title="Saldo devolvido na carteira", value=valor_desconto_cupom),
                ],
            ),
            extrato=[
                dict(
                    title="Extrato ida",
                    entries=[
                        dict(title="Reserva ida", subtitle="1 passageiro leito", value=valor_individual),
                        dict(
                            title="Taxa de revenda",
                            subtitle="1 passageiro",
                            value=D("0"),
                            type="taxa_revenda",
                            desconto_taxa_servico=D("0"),
                        ),
                        dict(title="Saldo utilizado", value=-valor_desconto_cupom),
                    ],
                    footer=dict(title="Total reserva ida", value=falta_pagar),
                ),
                dict(title="Total a pagar", value=falta_pagar),
                dict(title="Total ida", value=falta_pagar),
                dict(title="Total volta", value=D(0)),
            ],
            no_promo_alert="O cupom CUPOM_REMARCACAO não pode ser usado em viagens nesta data",
        )
        self._get_and_assert_extrato_remarcacao(client, nova_ida[0]["id"], grupo_id, extrato_esperado)
        with mock_mercadopago(value=falta_pagar):
            self.remarcar(client, nova_ida[0]["id"], grupo_id, payment=CARTAO, pay=falta_pagar)
        assert_accounting(client, count=14, saldo_real=D(0), saldo_bueda=D(0))

    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    def test_remarcacao_e_extrato_com_cupom_remove_pax_com_cupom_e_remarca(self, mock_n, mock_torre):
        # Valores
        count_seats = D("4")
        valor_individual = D("88.0")
        valor_desconto_cupom = valor_individual * D("0.5")
        pay_value = valor_individual * count_seats - valor_desconto_cupom
        CARTAO = CARTAO_MERCADO_DEFAULT.copy()
        CARTAO["value"] = pay_value

        client = self.client
        client.force_login(self.user_ze)

        CUPOM_CODE = "CUPOM_REMARCACAO"
        Cupom.objects.create(
            code=CUPOM_CODE,
            discount=D("0.50"),
            is_volta=True,
            type="PROMO",
            due_date=to_default_tz_required(dateutils.now() + timedelta(days=3)),
            datetime_ida_end=to_default_tz_required(dateutils.now() + timedelta(days=14)),
        )

        # Reserva ida e volta
        with mock_mercadopago(value=pay_value):
            travels = self.entra_no_grupo_ida_e_volta_separadas(
                client, payment=CARTAO, pay=pay_value, passengers=TWO_PAX, cupom=CUPOM_CODE
            )
        assert_accounting(client, count=6, saldo_real=D(0), saldo_bueda=D(0))

        # Vejo se o pax que ganhou a promo foi o primeiro mesmo para remove-lo
        pax_cancelar = travels[0]["passengers"][0]
        self.assertEqual(pax_cancelar["promocao"], "PROMO_CUPOM")
        self.remove_passageiro(client, travels[0]["id"], pax_cancelar["pid"])
        assert_accounting(client, count=8, saldo_real=valor_desconto_cupom, saldo_bueda=D(0))

        # Agora a travel não possui mais promo, o passageiro que havia foi removido
        grupo_id = self._get_grupo_bhsp()
        extrato_esperado = dict(
            extrato_cancelamento=dict(
                title="Reserva anterior",
                entries=[
                    dict(title="Cancelada", value=valor_individual),
                    dict(title="Saldo devolvido na carteira", value=valor_individual),
                ],
            ),
            extrato=[
                dict(
                    title="Extrato ida",
                    entries=[
                        dict(title="Reserva ida", subtitle="1 passageiro leito", value=valor_individual),
                        dict(
                            title="Taxa de revenda",
                            subtitle="1 passageiro",
                            value=D("0"),
                            type="taxa_revenda",
                            desconto_taxa_servico=D("0"),
                        ),
                        dict(title="Saldo utilizado", value=-valor_individual),
                    ],
                    footer=dict(title="Total reserva ida", value=D(0)),
                ),
                dict(title="Total a pagar", value=D(0)),
                dict(title="Total ida", value=D(0)),
                dict(title="Total volta", value=D(0)),
            ],
        )
        mock_torre.return_value = mocked_torre_response_no_info()
        self._get_and_assert_extrato_remarcacao(client, travels[0]["id"], grupo_id, extrato_esperado)
        with mock_mercadopago(value=88):
            self.remarcar(client, travels[0]["id"], grupo_id, payment=CARTAO, pay=D(0))
        assert_accounting(client, count=10, saldo_real=valor_desconto_cupom, saldo_bueda=D(0))

    @mock.patch("core.service.reserva.promocao.promo_cupom.now")
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    def test_remarcacao_e_extrato_com_cupom_fora_da_validade(self, mock_n, mock_torre, mock_now):
        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        valor_desconto_cupom = valor_individual * D("0.5")
        pay_value = valor_individual * count_seats - valor_desconto_cupom

        CARTAO = CARTAO_MERCADO_DEFAULT.copy()
        CARTAO["value"] = pay_value
        mock_now.return_value = timezone.now() + timedelta(minutes=1)
        client = self.client
        client.force_login(self.user_ze)

        CUPOM_CODE = "CUPOM_REMARCACAO"
        _3_dias_atras = dateutils.now() + timedelta(days=3)
        validade_cupom = to_default_tz_required(_3_dias_atras) or _3_dias_atras
        Cupom.objects.create(
            code=CUPOM_CODE,
            discount=D("0.50"),
            is_volta=True,
            type="PROMO",
            due_date=validade_cupom,
            datetime_ida_end=to_default_tz_required(dateutils.now() + timedelta(days=14)),
        )

        # Reserva ida e volta
        with mock_mercadopago(value=pay_value):
            travels = self.entra_no_grupo_ida_e_volta_separadas(
                client, payment=CARTAO, pay=pay_value, passengers=ONE_PAX, cupom=CUPOM_CODE
            )
        assert_accounting(client, count=4, saldo_real=D(0), saldo_bueda=D(0))

        # Mudo para 1 dia depois da validade do cupom, tenho que pagar a diferença
        mock_now.return_value = timezone.now() + timedelta(days=4)
        translation.activate("pt-br")
        due_date = date_format(to_default_tz_required(validade_cupom), "d/m/y à\\s H:i")
        grupo_id = self._get_grupo_bhsp()
        extrato_esperado = dict(
            extrato_cancelamento=dict(
                title="Reserva anterior",
                entries=[
                    dict(title="Cancelada", value=valor_desconto_cupom),
                    dict(title="Saldo devolvido na carteira", value=valor_desconto_cupom),
                ],
            ),
            extrato=[
                dict(
                    title="Extrato ida",
                    entries=[
                        dict(title="Reserva ida", subtitle="1 passageiro leito", value=valor_individual),
                        dict(
                            title="Taxa de revenda",
                            subtitle="1 passageiro",
                            value=D("0"),
                            type="taxa_revenda",
                            desconto_taxa_servico=D("0"),
                        ),
                        dict(title="Saldo utilizado", value=-valor_desconto_cupom),
                    ],
                    footer=dict(title="Total reserva ida", value=valor_desconto_cupom),
                ),
                dict(title="Total a pagar", value=valor_desconto_cupom),
                dict(title="Total ida", value=valor_desconto_cupom),
                dict(title="Total volta", value=D(0)),
            ],
            no_promo_alert="Essa promoção encerrou em %s" % due_date,
        )
        mock_torre.return_value = mocked_torre_response_no_info()
        self._get_and_assert_extrato_remarcacao(client, travels[0]["id"], grupo_id, extrato_esperado)

        with mock_mercadopago(value=44):
            self.remarcar(client, travels[0]["id"], grupo_id, payment=CARTAO, pay=valor_desconto_cupom)
        assert_accounting(client, count=8, saldo_real=D(0), saldo_bueda=D(0))

    @mock.patch("core.service.notifications.user_notification_svc._send_travel_notifications")
    @mock_mercadopago(value=D("176"))
    @mock.patch("core.service.torre_de_controle_svc.get_telemetry_info_from_group_and_place_boarding")
    @mock.patch("core.service.reserva.reserva_svc.emitir_passagens_async")
    @mock.patch.object(MarketplaceSeatsController, "escolhe_e_bloqueia_poltronas")
    @mock.patch.object(MarketplaceSeatsController, "_raise_for_invalid_trecho_classe")
    def test_remarcacao_marketplace(self, mock_s, mock_poltronas, mock_emitir_passagens, mock_torre, mock_n):
        client = self.client
        client.force_login(self.user_ze)

        # Valores
        count_seats = 2
        valor_individual = D("88.0")
        pay_value = valor_individual * count_seats

        # Reserva ida e volta
        travels = self.entra_no_grupo_ida_e_volta_separadas(
            client, payment=CARTAO_MERCADO_DEFAULT, pay=pay_value, passengers=ONE_PAX
        )
        assert_accounting(client, count=3, saldo_real=D(0))

        # Vejo o extrato de remarcação da ida
        grupo_id = self._get_grupo_bhsp(modelo_venda=Grupo.ModeloVenda.MARKETPLACE)
        extrato_esperado = dict(
            extrato_cancelamento=dict(
                title="Reserva anterior",
                entries=[
                    dict(title="Cancelada", value=valor_individual),
                    dict(title="Saldo devolvido na carteira", value=valor_individual),
                ],
            ),
            extrato=[
                dict(
                    title="Extrato ida",
                    entries=[
                        dict(title="Reserva ida", subtitle="1 passageiro leito", value=valor_individual),
                        dict(
                            title="Taxa de revenda",
                            subtitle="1 passageiro",
                            value=D("0"),
                            type="taxa_revenda",
                            desconto_taxa_servico=D("0"),
                        ),
                        dict(title="Saldo utilizado", value=-valor_individual),
                    ],
                    footer=dict(title="Total reserva ida", value=D(0)),
                ),
                dict(title="Total a pagar", value=D(0)),
                dict(title="Total ida", value=D(0)),
                dict(title="Total volta", value=D(0)),
            ],
        )
        mock_torre.return_value = mocked_torre_response_no_info()
        mock_poltronas.return_value = [
            BlockedSeat(
                poltrona=Assento(numero=42, x=1, y=1, tipo_assento="convencional"),
                tempo_limite_bloqueio=None,
                external_payload={"opa": "opa"},
            )
        ]
        self._get_and_assert_extrato_remarcacao(client, travels[0]["id"], grupo_id, extrato_esperado)

        # Faço a remarcação
        travels_remarcadas = self.remarcar(client, travels[0]["id"], grupo_id)

        if not travels_remarcadas:
            raise ValueError("Algo deu errado na remarcação")

        travel_remarcada_id = travels_remarcadas[0]["id"]
        travel_remarcada = Travel.objects.get(pk=travel_remarcada_id)
        self.assertIsNotNone(travel_remarcada.reserva)
        self.assertIsNotNone(travel_remarcada.reserva_id)
        assert_accounting(client, count=5, saldo_real=D(0), saldo_bueda=D(0))
        assert travel_remarcada.passageiro_set.first().poltrona == 42
        assert travel_remarcada.passageiro_set.first().mkp_extra_bloqueio_poltrona is not None
        mock_emitir_passagens.delay.assert_called_with([travel["id"] for travel in travels_remarcadas])

    def _get_grupo_bhsp(
        self, ida=True, hashed=True, max_split_value=None, add_dias=None, modelo_venda=Grupo.ModeloVenda.BUSER
    ):
        trecho = self.b.trecho_classe_bhsp if ida else self.b.trecho_classe_spbh
        if max_split_value:
            trecho.max_split_value = max_split_value
            trecho.save()
        if add_dias:
            trecho.datetime_ida = trecho.datetime_ida + timedelta(days=add_dias)
            trecho.grupo.datetime_ida = trecho.datetime_ida
            trecho.save()

        new_trecho = copy.copy(trecho)
        new_trecho.id = None
        new_trecho.price_manager = None
        new_trecho.save()

        new_trecho.grupo.modelo_venda = modelo_venda
        new_trecho.grupo.save()

        new_trecho.refresh_from_db()

        if hashed:
            return utils.hashint(new_trecho.id) if new_trecho.id is not None else None
        else:
            return new_trecho.id

    def _get_grupo_bhipa(self, ida=True, hashed=True, max_split_value=None, add_dias=None):
        trecho = self.b.trecho_classe_bhipa if ida else self.b.trecho_classe_ipabh
        if max_split_value:
            trecho.max_split_value = max_split_value
            trecho.save()
        if add_dias:
            trecho.datetime_ida = trecho.datetime_ida + timedelta(days=add_dias)
            trecho.save()
        return utils.hashint(trecho.id) if hashed else trecho.id


PAX_ONE = {
    "name": "Mamae",
    "rg_number": "12345678",
    "rg_orgao": "SSP/SP",
    "tipo_documento": "RG",
    "cpf": "699.922.830-25",
    "birthday": "24/11/1962",
}

PAX_TWO = {
    "name": "Papis",
    "rg_number": "87654321",
    "rg_orgao": "SSP/SP",
    "tipo_documento": "RG",
    "cpf": "607.756.510-52",
    "birthday": "17/06/1955",
}

ONE_PAX = [PAX_ONE]

TWO_PAX = [PAX_ONE, PAX_TWO]
