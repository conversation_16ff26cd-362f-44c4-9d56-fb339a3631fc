from datetime import timedelta

from django.contrib.auth.models import User

from commons.dateutils import now
from core.enums import CategoriaEspecial
from core.models_grupo import Grupo, TrechoClasse
from core.service import rodoviaria_svc
from core.service.selecao_assento import (
    NoSeatLayoutAvailable,
)
from core.service.selecao_assento.marketplace import MarketplaceSeatsController
from core.service.selecao_assento.models import (
    Assento,
    BlockedSeat,
)


class HibridoSeatsController(MarketplaceSeatsController):
    def __init__(self, trecho_classe: TrechoClasse | int, user: User | None = None):
        super().__init__(trecho_classe, user)
        self._raise_for_invalid_trecho_classe()

    def _raise_for_invalid_trecho_classe(self):
        if self.trecho_classe.grupo.modelo_venda not in [Grupo.ModeloVenda.HIBRIDO] or not rodoviaria_svc._is_integrado(
            self.trecho_classe_id
        ):
            raise NoSeatLayoutAvailable()

    def has_marcacao_assento(self) -> bool:
        return False

    def bloquear_poltrona(
        self,
        poltrona: Assento,
        categoria_especial: CategoriaEspecial = CategoriaEspecial.NORMAL,
        timeout: int | None = None,
    ) -> BlockedSeat:
        return BlockedSeat(poltrona=poltrona, tempo_limite_bloqueio=now() + timedelta(hours=24), external_payload={})

    def desbloquear_poltrona(self, poltrona_bloqueada: BlockedSeat) -> bool:
        return True
