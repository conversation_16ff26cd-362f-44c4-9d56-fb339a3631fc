import logging
from collections import defaultdict

from beeline import traced
from celery import shared_task
from tenacity import retry, retry_if_exception_type, stop_after_attempt

from commons.memoize import memoize
from core.enums import CategoriaEspecial
from core.forms.rodoviaria_forms import BuseiroForm, ComprarForm, DadosBeneficioForm
from core.models_grupo import Grupo
from core.models_travel import (
    Buseiro,
    Passageiro,
    Travel,
)
from core.service import globalsettings_svc, rodoviaria_svc
from core.service.selecao_assento import SeatAlreadyTaken, SeatNotBlocked, SeatSelectionConnectionError
from core.service.selecao_assento import marketplace as marketplace_selecao_assento
from core.service.selecao_assento.models import Assento, BlockedSeat
from integrations import get_client
from integrations.rodoviaria_client import RodoviariaClient
from integrations.rodoviaria_client.exceptions import (
    PoltronaExpiradaException,
    RodoviariaConnectionException,
    RodoviariaException,
    RodoviariaViagemIndisponivel,
)
from recommender.adapters import abstractmethod

buserlogger = logging.getLogger("buserlogger")


def passagens_emitidas(travels: list[Travel]) -> bool:
    travels_rodoviaria = [t for t in travels if rodoviaria_svc._is_integrado(t.trecho_classe_id)]

    if not travels_rodoviaria:
        return True

    travels_ids = [t.id for t in travels_rodoviaria]
    buseiros_ids = list(Buseiro.objects.filter(passageiro__travel_id=travels_ids[0]).values_list("id", flat=True))
    passagens_emitidas = rodoviaria_svc.get_passagens_rodoviaria(travels_ids, buseiros_ids, "confirmada")

    # casos de conexão podem gerar mais de uma passagem por buseiro, por isso checa a quantidade minima, não exata
    todas_emitidas = bool(passagens_emitidas and len(passagens_emitidas) >= len(travels_ids) * len(buseiros_ids))

    buserlogger.info(
        "[rodoviaria_reserva_apos_pgto] _existe_passagens_emitidas_rodoviaria",
        extra={
            "existe_passagens_emitidas": todas_emitidas,
            "passagens_emitidas": passagens_emitidas,
            "travels_rodoviaria": travels_rodoviaria,
        },
    )

    return todas_emitidas


@traced("reserva_rodoviaria_svc.emitir_passagens_rodoviaria")
def emitir_passagens_rodoviaria(travels: list[Travel], timeout=180):
    travels_rodoviaria = [t for t in travels if rodoviaria_svc._is_integrado(t.trecho_classe_id, only_marketplace=True)]

    if not travels_rodoviaria:
        return

    passageiros_by_travel = defaultdict(list)
    for pax in Passageiro.objects.select_related("travel__trecho_classe").filter(travel__in=travels_rodoviaria):
        passageiros_by_travel[pax.travel].append(pax)

    for travel, passageiros in passageiros_by_travel.items():
        categoria_especial, dados_beneficio = travel.reserva.categoria_especial_info()  # type: ignore
        seat_controller = marketplace_selecao_assento.MarketplaceSeatsController(travel.trecho_classe)
        for pax in passageiros:
            EmissaoMarketplace(pax, seat_controller, categoria_especial, dados_beneficio).emit(timeout)


def get_vagas_por_categoria_especial(trecho_classe_id, company_id):
    empresa_info = rodoviaria_svc.get_empresa_info(company_id)

    if not (empresa_info and empresa_info.get("features") and "active" in empresa_info["features"]):
        return {}

    client: RodoviariaClient = get_client("rodoviaria")
    try:
        return client.get_vagas_por_categoria_especial(trecho_classe_id)
    except client.exceptions.HTTPErrorNotFound as ex:
        raise RodoviariaViagemIndisponivel from ex
    except client.exceptions.ClientError:
        raise RodoviariaException("Não foi possível buscar categoria especiais disponíveis")


def remanejamento_async(trecho_classe_origem, trecho_classe_destino, remanejamentos):
    is_trechoclasse_origem_integrado = rodoviaria_svc._is_integrado(trecho_classe_origem.id)
    is_trechoclasse_destino_integrado = rodoviaria_svc._is_integrado(trecho_classe_destino.id)
    if not (is_trechoclasse_origem_integrado or is_trechoclasse_destino_integrado):
        return

    use_v2 = globalsettings_svc.get("rodoviaria_remanejamento_v2", False)
    if use_v2:
        for remanejamento in remanejamentos:
            remaneja_passageiro_task.delay(remanejamento.travel.id, remanejamento.travel_remanejada.id)  # type: ignore
    else:
        remanejamentos_rodoviaria = []

        for remanejamento in remanejamentos:
            buseiros = [p.buseiro for p in remanejamento.travel.passageiro_set.all()]
            params = _remaneja_passageiros_params(remanejamento.travel, remanejamento.travel_remanejada, None, buseiros)
            remanejamentos_rodoviaria.append(params)
        return rodoviaria_svc._client.remanejamento_async(remanejamentos_rodoviaria)


@shared_task(queue="rodoviaria_emissao_async")
def remaneja_passageiro_task(travel_id: int, travel_destino_id: int):
    try:
        travel = Travel.objects.get(id=travel_id)
        travel_destino = Travel.objects.get(id=travel_destino_id)
    except Travel.DoesNotExist:
        buserlogger.info(
            "remaneja_passageiro_task.travel_not_found",
            extra={"travel_id": travel_id, "travel_destino_id": travel_destino_id},
        )
        return
    remaneja_passageiro(travel, travel_destino)


def remaneja_passageiro(travel: Travel, travel_destino: Travel):
    """
    travel.reservation_code: código gerado aleatoriamente
    travel_destino.reservation_code: código de reserva original
    """
    is_trechoclasse_origem_integrado = rodoviaria_svc.is_integrado_and_marketplace(travel.trecho_classe)
    is_trechoclasse_destino_integrado = rodoviaria_svc.is_integrado_and_marketplace(travel_destino.trecho_classe)
    if not is_trechoclasse_origem_integrado and not is_trechoclasse_destino_integrado:
        return

    categoria_especial, dados_beneficio = (
        travel_destino.reserva.categoria_especial_info() if travel_destino.reserva else (CategoriaEspecial.NORMAL, None)
    )  # type: ignore
    if categoria_especial != CategoriaEspecial.NORMAL:
        raise RodoviariaException("Reserva de categoria especial não pode ser remanejada")

    if is_trechoclasse_origem_integrado:
        # Não continua com remanejamento se não tiver antecedencia pra cancelar travel origem
        rodoviaria_svc._raise_for_antecedencia_cancelamento(travel)

    if is_trechoclasse_destino_integrado:
        passageiros = list(travel_destino.passageiro_set.all())
        seat_controller = marketplace_selecao_assento.MarketplaceSeatsController(travel_destino.trecho_classe)
        for pax in passageiros:
            EmissaoMarketplace(pax, seat_controller, categoria_especial, dados_beneficio).emit()

    if is_trechoclasse_origem_integrado:
        try:
            rodoviaria_svc.efetua_cancelamento(travel)
        except Exception as ex:
            rodoviaria_svc.efetua_cancelamento(travel_destino)
            raise ex


def _remaneja_passageiros_params(travel, travel_destino, poltronas_destino, buseiros):
    passageiros = [
        {
            "id": buseiro.id,
            "name": buseiro.name,
            "rg_number": buseiro.rg_number,
            "cpf": buseiro.cpf,
            "phone": buseiro.phone,
            "tipo_documento": "RG",
        }
        for buseiro in buseiros
    ]
    return {
        "travel_id": travel.id,
        "travel_destino_id": travel_destino.id,
        "reservation_code": travel.reservation_code,
        "travel_max_split_value": travel_destino.max_split_value,
        "trechoclasse_origem_id": travel.trecho_classe.id,
        "trechoclasse_destino_id": travel_destino.trecho_classe.id,
        "grupo_destino_id": travel_destino.grupo_id,
        "company_origem_id": travel.trecho_classe.grupo.company_id,
        "modelo_venda_origem": travel.trecho_classe.grupo.modelo_venda,
        "company_destino_id": travel_destino.trecho_classe.grupo.company_id,
        "modelo_venda_destino": travel_destino.trecho_classe.grupo.modelo_venda,
        "passengers": passageiros,
        "poltronas_destino": poltronas_destino,
    }


def emitir_passagem_staff(travel_id, buseiro_id, buyer_cpf, numero_poltrona: int | None = None):
    pax = Passageiro.objects.select_related(
        "travel", "buseiro", "travel__trecho_classe", "travel__reserva", "buseiro__user"
    ).get(travel_id=travel_id, buseiro_id=buseiro_id)

    if pax.foi_emitido and not passagens_emitidas([pax.travel]):
        # TODO remover
        # setando cancelamento aqui apenas pra manter compatibilidade com o fluxo antigo
        pax.set_cancelamento_emissao_marketplace()

    pax.poltrona = numero_poltrona or pax.poltrona
    EmissaoMarketplace(pax).emit()
    pax.set_emissao_staff()


class EmissaoBase:
    @abstractmethod
    def validate(self):
        pass

    @abstractmethod
    def set_blocked_seat(self, numero_poltrona: int) -> BlockedSeat:
        pass

    @abstractmethod
    def ensure_blocked_seat(self) -> BlockedSeat:
        pass

    @abstractmethod
    def emit(self):
        pass


class EmissaoMarketplace(EmissaoBase):
    def __init__(
        self,
        passageiro: Passageiro,
        seats_controller: marketplace_selecao_assento.MarketplaceSeatsController | None = None,
        categoria_especial: str | None = None,
        dados_beneficio: DadosBeneficioForm | None = None,
    ):
        self.passageiro = passageiro
        self.seats_controller = seats_controller or marketplace_selecao_assento.MarketplaceSeatsController(
            passageiro.travel.trecho_classe
        )
        self.validate()
        self.categoria_especial = categoria_especial
        self.dados_beneficio = dados_beneficio
        if categoria_especial is None:
            self.categoria_especial, self.dados_beneficio = (
                passageiro.travel.reserva.categoria_especial_info()
                if passageiro.travel.reserva
                else (CategoriaEspecial.NORMAL, None)
            )  # type: ignore

    def validate(self):
        if self.passageiro.travel.trecho_classe.grupo.modelo_venda != Grupo.ModeloVenda.MARKETPLACE:
            raise Exception("Emissão de passageiros é permitida apenas para Marketplace")
        if not rodoviaria_svc._is_integrado(self.passageiro.travel.trecho_classe_id):
            raise Exception("Emissão de passageiros não é permitida para viagens não integradas.")

        # casos de conexão podem gerar mais de uma passagem por buseiro, por isso checa a quantidade minima, não exata
        passagens_emitidas = rodoviaria_svc.get_passagens_rodoviaria(
            [self.passageiro.travel_id], [self.passageiro.buseiro_id], "confirmada"
        )
        todas_emitidas = bool(passagens_emitidas and len(passagens_emitidas) >= 1)
        if todas_emitidas:
            raise Exception("Passagem já emitida")

    def set_blocked_seat(self, numero_poltrona: int) -> BlockedSeat:
        blocked_seat = self.seats_controller.bloquear_poltrona(Assento.from_numero_poltrona(numero_poltrona))
        self._save_blocked_seat_on_pax(blocked_seat)
        return blocked_seat

    def ensure_blocked_seat(self) -> BlockedSeat:
        # A poltrona está salva no passageiro e válida
        if self.passageiro.typed_extra and "bloqueio_poltrona" in self.passageiro.typed_extra:
            seat = BlockedSeat.model_validate(self.passageiro.typed_extra["bloqueio_poltrona"])
            if not seat.expired:
                return seat

        # Tenta bloquear a mesma poltrona que o passageiro escolheu
        if self.passageiro.poltrona:
            try:
                return self.set_blocked_seat(self.passageiro.poltrona)  # type: ignore
            except SeatAlreadyTaken:
                self.passageiro.extra["seat_already_taken"] = True
                pass
            except (SeatSelectionConnectionError, SeatNotBlocked) as ex:
                raise RodoviariaConnectionException from ex

        # Tenta bloquear outra poltrona
        try:
            blocked_seat = self.seats_controller.escolhe_e_bloqueia_poltronas(1, self.categoria_especial)[0]
            self._save_blocked_seat_on_pax(blocked_seat)
            return blocked_seat
        except (SeatSelectionConnectionError, SeatNotBlocked) as ex:
            raise RodoviariaConnectionException from ex

    def _save_blocked_seat_on_pax(self, blocked_seat: BlockedSeat):
        self.passageiro.poltrona = blocked_seat.poltrona.numero
        if self.passageiro.extra is None:
            self.passageiro.extra = {}
        self.passageiro.extra["bloqueio_poltrona"] = blocked_seat.model_dump()
        self.passageiro.save()

    @retry(retry=retry_if_exception_type(PoltronaExpiradaException), stop=stop_after_attempt(2), reraise=True)
    def emit(self, timeout: int = 180):
        self.ensure_blocked_seat()
        try:
            form = self.build_request_form(self.passageiro, self.categoria_especial, self.dados_beneficio)
            response = rodoviaria_svc.efetua_compra_unica(form, timeout=timeout)
            self.passageiro.set_emissao_marketplace(response)  # type: ignore
            return response
        except Exception as ex:
            if type(ex) == PoltronaExpiradaException:
                self.passageiro.set_extra_expired_blocked_seat()
            self.passageiro.set_erro_emissao_marketplace(ex)
            raise ex

    @staticmethod
    def build_request_form(
        passageiro: Passageiro, categoria_especial: str | None = None, dados_beneficio: DadosBeneficioForm | None = None
    ) -> ComprarForm:
        buseiro = passageiro.buseiro
        travel = passageiro.travel
        blocked_seat = BlockedSeat.model_validate(passageiro.typed_extra["bloqueio_poltrona"])
        buyer_cpf = EmissaoMarketplace.buyer_cpf(passageiro.travel)
        buyer_phone = EmissaoMarketplace.buyer_phone(passageiro.travel)

        form = ComprarForm(
            trechoclasse_id=travel.trecho_classe_id,
            travel_id=travel.id,
            valor_cheio=travel.max_split_value,
            poltronas=[passageiro.poltrona],  # type: ignore
            extra_poltronas=blocked_seat.external_payload,
            buseiros=[
                BuseiroForm(
                    id=buseiro.id,
                    name=buseiro.name,
                    cpf=buseiro.cpf,
                    buyer_cpf=buyer_cpf,
                    rg_number=buseiro.rg_number,
                    rg_orgao=buseiro.rg_orgao,
                    tipo_documento=buseiro.tipo_documento,
                    phone=buyer_phone,
                    birthday=buseiro.birthday,
                    dados_beneficio=dados_beneficio,
                )
            ],
            categoria_especial=categoria_especial or CategoriaEspecial.NORMAL,  # type: ignore
        )
        return form

    @staticmethod
    @memoize(timeout=60 * 60 * 24)
    def buyer_cpf(travel: Travel) -> str | None:
        return (
            travel.user.profile.cpf
            if travel.user and getattr(travel.user, "profile", None) and travel.user.profile.cpf
            else next(iter(_get_passageiros_data(travel.id)[0]), None)
        )

    @staticmethod
    @memoize(timeout=60 * 60 * 24)
    def buyer_phone(travel: Travel) -> str | None:
        return (
            travel.user.profile.cell_phone
            if travel.user and getattr(travel.user, "profile", None) and travel.user.profile.cell_phone
            else next(iter(_get_passageiros_data(travel.id)[1]), None)
        )


def _get_passageiros_data(travel_id: int) -> tuple[list[str], list[str]]:
    passageiros = Passageiro.objects.filter(travel_id=travel_id).values(
        "buseiro__cpf", "buseiro__user__profile__cell_phone"
    )
    return [pax["buseiro__cpf"] for pax in passageiros], [
        pax["buseiro__user__profile__cell_phone"] for pax in passageiros
    ]
